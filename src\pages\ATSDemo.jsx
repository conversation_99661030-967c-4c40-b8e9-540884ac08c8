import React, { useRef, useState, useEffect } from 'react';

// Import necessary icons from react-icons/fa
import {
  FaPlay, FaPause, FaVolumeUp, FaVolumeOff, FaExpand, FaUsers,
  FaChartLine, FaFastBackward, FaFastForward, FaChartBar, FaShieldAlt, FaRobot,
  FaMobileAlt, FaSearch, FaUserCheck, FaComments, FaPaperPlane, FaFileAlt
} from 'react-icons/fa';

// NOTE: If you are using a local file with Create React App,
// you must import it so webpack can process it.
import ATSVideoAsset from '../Asserts/ATS.mp4';

const ATSDemo = () => {
  // --- STATE AND REFS ---
  const videoRef = useRef(null);
  const heroRef = useRef(null);
  const pipelineContainerRef = useRef(null); // Ref for the new 3D visualization

  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [videoError, setVideoError] = useState(null);

  // --- COMPONENT DATA ---
  // --- FIX/ENHANCEMENT #1 ---
  // Replaced the placeholder image URL with a valid sample video URL.
  // Replace this with your own video file. If it's a local file, import it first.
  const ATSVideo = ATSVideoAsset;
  // const ATSVideo = ATSVideoAsset; // <-- Use this if you import the local file

  const features = [
    { icon: FaFileAlt, title: "Candidate Management", description: "Streamline hiring with full candidate tracking from source to hire." },
    { icon: FaChartBar, title: "Analytics & Reporting", description: "Get recruitment insights to make data-driven hiring decisions." },
    { icon: FaRobot, title: "AI-Powered Automation", description: "Reduce hiring time by 50% with intelligent workflow automation." },
    { icon: FaShieldAlt, title: "Secure & Compliant", description: "Enterprise-grade security with GDPR-compliant data protection." }
  ];

  const demoKeyPoints = [
    { icon: FaUsers, text: "Real-time Collaboration" },
    { icon: FaChartLine, text: "Advanced Analytics" },
    { icon: FaRobot, text: "AI-Powered Matching" },
    { icon: FaMobileAlt, text: "Mobile Optimized" }
  ];

  const benefits = [
    "Automated resume parsing", "Integrated video interviewing", "Customizable hiring workflows",
    "Real-time team collaboration", "Mobile-responsive for all users", "API integrations with HR tools",
    "Advanced candidate sourcing", "Comprehensive analytics"
  ];

  // --- 3D TALENT PIPELINE EFFECT ---
  useEffect(() => {
    // This effect remains the same, as it was functioning correctly.
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js';
    script.async = true;
    document.body.appendChild(script);

    script.onload = () => {
      if (!pipelineContainerRef.current || typeof THREE === 'undefined') return;

      const container = pipelineContainerRef.current;
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
      camera.position.z = 300;

      const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
      renderer.setSize(container.clientWidth, container.clientHeight);
      container.appendChild(renderer.domElement);

      const particlesCount = 2000;
      const positions = new Float32Array(particlesCount * 3);
      const colors = new Float32Array(particlesCount * 3);
      const velocities = [];

      const colorSelected = new THREE.Color(0x00a0e9);
      const colorPool = new THREE.Color(0x4a5568);

      for (let i = 0; i < particlesCount; i++) {
        positions[i * 3] = (Math.random() * 2 - 1) * 500;
        positions[i * 3 + 1] = (Math.random() * 2 - 1) * 500;
        positions[i * 3 + 2] = (Math.random() * 2 - 1) * 500;
        
        velocities.push({
          x: (Math.random() - 0.5) * 0.1,
          y: (Math.random() - 0.5) * 0.1,
          z: (Math.random() - 0.5) * 0.1,
          isTargeted: Math.random() < 0.1 // 10% are targeted
        });
        
        if (velocities[i].isTargeted) {
          colorSelected.toArray(colors, i * 3);
        } else {
          colorPool.toArray(colors, i * 3);
        }
      }

      const particlesGeometry = new THREE.BufferGeometry();
      particlesGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

      const particlesMaterial = new THREE.PointsMaterial({
        size: 1.5,
        vertexColors: true,
        transparent: true,
        opacity: 0.8,
        sizeAttenuation: true
      });

      const particleSystem = new THREE.Points(particlesGeometry, particlesMaterial);
      scene.add(particleSystem);

      const target = new THREE.Vector3(0, 0, 0);

      const animate = () => {
        requestAnimationFrame(animate);
        const positions = particleSystem.geometry.attributes.position.array;

        for (let i = 0; i < particlesCount; i++) {
          if (velocities[i].isTargeted) {
            const currentPos = new THREE.Vector3(positions[i*3], positions[i*3+1], positions[i*3+2]);
            currentPos.lerp(target, 0.005);
            positions[i*3] = currentPos.x;
            positions[i*3+1] = currentPos.y;
            positions[i*3+2] = currentPos.z;

            if(currentPos.distanceTo(target) < 1) {
              positions[i*3] = (Math.random() * 2 - 1) * 500;
              positions[i*3+1] = (Math.random() * 2 - 1) * 500;
              positions[i*3+2] = (Math.random() * 2 - 1) * 500;
            }
          } else {
            positions[i*3] += velocities[i].x;
            positions[i*3+1] += velocities[i].y;
            positions[i*3+2] += velocities[i].z;

            if (Math.abs(positions[i*3]) > 500 || Math.abs(positions[i*3+1]) > 500 || Math.abs(positions[i*3+2]) > 500) {
              positions[i*3] = (Math.random() * 2 - 1) * 500;
              positions[i*3+1] = (Math.random() * 2 - 1) * 500;
              positions[i*3+2] = (Math.random() * 2 - 1) * 500;
            }
          }
        }
        
        particleSystem.geometry.attributes.position.needsUpdate = true;
        particleSystem.rotation.y += 0.0002;
        renderer.render(scene, camera);
      };
      animate();

      const handleResize = () => {
        if (!container) return;
        camera.aspect = container.clientWidth / container.clientHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(container.clientWidth, container.clientHeight);
      };
      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (container && renderer.domElement.parentNode) {
            container.removeChild(renderer.domElement);
        }
        if (script.parentNode) {
            document.body.removeChild(script);
        }
      };
    };

    return () => {
      if (script.parentNode) {
        document.body.removeChild(script);
      }
    };
  }, []);


  // --- VIDEO PLAYER HANDLERS ---
  // --- FIX/ENHANCEMENT #2 ---
  // Made the function async to handle the promise returned by video.play().
  // This prevents potential "Uncaught (in promise)" errors in the console.
  const handlePlayPause = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        try {
          await videoRef.current.play();
        } catch (error) {
          console.error("Video play failed:", error);
          // Set an error state if playback is blocked by the browser
          setVideoError("Playback was prevented. Please click the play button again.");
        }
      }
      // --- FIX/ENHANCEMENT #3 ---
      // Removed setIsPlaying(!isPlaying) from here. The video's onPlay and onPause
      // event handlers are the most reliable source of truth for its state.
    }
  };

  const handleTimeUpdate = () => videoRef.current && setCurrentTime(videoRef.current.currentTime);
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setIsVideoLoading(false);
    }
  };
  const handleProgressClick = (e) => {
    if (videoRef.current) {
      const progressBar = e.currentTarget;
      const clickX = e.nativeEvent.offsetX;
      const newTime = (clickX / progressBar.offsetWidth) * duration;
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };
  const formatTime = (time) => {
    if (isNaN(time) || time === 0) return '00:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  const handleVolumeToggle = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };
  const handleFullscreen = () => {
    const videoElement = videoRef.current;
    if (!videoElement) return;
    if (videoElement.requestFullscreen) videoElement.requestFullscreen();
    else if (videoElement.mozRequestFullScreen) videoElement.mozRequestFullScreen();
    else if (videoElement.webkitRequestFullscreen) videoElement.webkitRequestFullscreen();
    else if (videoElement.msRequestFullscreen) videoElement.msRequestFullscreen();
  };
  const handleSkipForward = () => videoRef.current && (videoRef.current.currentTime += 10);
  const handleSkipBackward = () => videoRef.current && (videoRef.current.currentTime -= 10);
  const handleCanPlay = () => { setIsVideoLoading(false); setVideoError(null); };
  const handleVideoError = (e) => {
      // Provide a more specific error message
      setVideoError(`This video could not be played. Error code: ${e.target.error.code}`);
      setIsVideoLoading(false);
  };


  // --- INTERSECTION OBSERVER FOR ANIMATIONS ---
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('is-visible');
            observer.unobserve(entry.target);
          }
        });
      }, { threshold: 0.1 }
    );

    const elements = document.querySelectorAll('.animate-on-scroll');
    elements.forEach(el => observer.observe(el));

    return () => elements.forEach(el => observer.unobserve(el));
  }, []);

  return (
    <div className="ats-page-wrapper" style={{ background: '#012855', color: '#f1f5f9', minHeight: '100vh' }}>
      <style jsx>{`
        /* --- Global Styles & Animations --- */
        .ats-page-wrapper {
            overflow-x: hidden;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #001A3A;
        }
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(40px);
            transition: opacity 0.8s ease-out, transform 0.8s ease-out;
        }
        .animate-on-scroll.is-visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* --- Enhanced ATS Hero Section --- */
        .hero-section {
            position: relative;
            height: 100vh;
            background: radial-gradient(ellipse at bottom, #002b59 0%, #001a3a 70%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            padding: 2rem 1rem;
        }
        .hero-content {
            text-align: center;
            position: relative;
            z-index: 5; /* Ensure text is above the visualization */
        }
        .hero-title {
            font-size: clamp(2.8rem, 6vw, 4.5rem);
            font-weight: 800;
            line-height: 1.15;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 40%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
        }
        .hero-subtitle {
            font-size: clamp(1rem, 2.5vw, 1.25rem);
            color: rgba(255, 255, 255, 0.8);
            max-width: 650px;
            margin: 0 auto 2.5rem auto;
            line-height: 1.7;
        }
        .hero-cta {
            display: inline-block;
            padding: 14px 35px;
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            color: white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 10px 30px rgba(0, 160, 233, 0.3);
            transition: all 0.3s ease;
        }
        .hero-cta:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 160, 233, 0.4);
        }

        /* 3D Talent Pipeline Visualization */
        .pipeline-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        /* --- Demo Section --- */
        .new-demo-section {
            padding: 2rem 1rem;
            background: #ffffff;
            text-align: center;
        }
        .new-demo-section h2 {
            font-size: clamp(2.2rem, 5vw, 3rem);
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
        }
        .new-demo-section p {
            font-size: 1.1rem;
            color: #33475b;
            max-width: 800px;
            margin: 0 auto 2.5rem auto;
            line-height: 1.7;
        }
        .demo-key-points-grid {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1.5rem;
        }
        .demo-key-point {
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            border-radius: 0.75rem;
            padding: 1.25rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            flex-basis: 200px;
            text-align: center;
        }
        .demo-key-point:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 25px rgba(0, 160, 233, 0.3);
        }
        .demo-key-point :global(.icon) { font-size: 2rem; }

        /* --- Video & Features Section --- */
        .video-features-benefits-section {
            padding: 2rem 1rem;
            background: #f8f9fa;
        }
        .video-container {
            background: #000;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 43, 89, 0.2);
            position: relative;
            overflow: hidden;
            border-radius: 1rem;
            aspect-ratio: 16 / 9;
        }
        .video-container video { width: 100%; height: 100%; object-fit: cover; }
        .feature-list-container {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        .feature-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0, 41, 86, 0.05);
            transition: all 0.4s ease;
            padding: 1.5rem;
            border-radius: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 1.25rem;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0, 43, 89, 0.1);
            border-color: rgba(0, 157, 230, 0.5);
        }
        .feature-icon {
            width: 55px; height: 55px; min-width: 55px;
            border-radius: 0.5rem;
            display: flex; align-items: center; justify-content: center;
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            color: white;
        }
        .feature-icon :global(svg) { font-size: 25px; }
        .feature-card h5 {
            color: #001A3A; font-size: 1.1rem;
            font-weight: 700; margin-bottom: 0.5rem;
        }
        .feature-card p {
            color: #33475b; font-size: 0.95rem;
            line-height: 1.6; margin: 0;
        }
        
        /* Video Player Styles */
        .video-overlay {
            position: absolute; top: 0; left: 0; width: 100%; height: 100%;
            display: flex; align-items: center; justify-content: center;
            background-color: rgba(0,0,0,0.5); z-index: 5;
            transition: opacity 0.3s ease;
        }
        .video-container:hover .video-overlay-hidden {
            opacity: 1 !important;
        }
        @keyframes playButtonPulse {
            50% { box-shadow: 0 0 0 15px rgba(0, 160, 233, 0.3); transform: scale(1.1); }
        }
        .play-button {
            background: linear-gradient(135deg, rgba(0, 160, 233, 0.9) 0%, rgba(0, 86, 179, 0.9) 100%);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            width: 90px; height: 90px;
            border-radius: 50%; cursor: pointer;
            display: flex; align-items: center; justify-content: center;
            animation: playButtonPulse 3s ease-in-out infinite;
            transition: transform 0.3s;
        }
        .play-button:hover { transform: scale(1.1); }
        .video-controls {
            position: absolute; bottom: 0; left: 0; right: 0;
            opacity: 0; transform: translateY(100%);
            transition: all 0.4s ease-out;
            pointer-events: none;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 0.75rem 1rem; z-index: 10;
        }
        .video-container:hover .video-controls {
            opacity: 1; transform: translateY(0);
            pointer-events: all;
        }
        .video-controls .btn {
            background: transparent; border: none;
            color: white; font-size: 1.1rem; padding: 0.5rem;
        }
        .video-controls .btn:hover { color: #00a0e9; }
        .progress-bar-container {
            flex-grow: 1; height: 8px;
            background-color: rgba(255,255,255,0.3);
            border-radius: 4px; cursor: pointer; margin: 0 1rem;
        }
        .progress-bar {
            height: 100%; background-color: #00a0e9;
            border-radius: 4px;
        }
        .time-display {
            font-size: 0.9rem; color: white;
            min-width: 90px; text-align: center;
        }

        /* --- Benefits Section --- */
        .benefits-section {
            padding: 2rem 1rem;
            background: #f8f9fa;
        }
        .benefits-section h2 {
            font-size: clamp(2.2rem, 5vw, 3rem);
            font-weight: 800; text-align: center; margin-bottom: 3rem;
            background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
        }
        .benefit-box {
            background: white;
            border: 1px solid #e9ecef;
            transition: all 0.4s ease;
            padding: 2rem; border-radius: 1rem;
            height: 100%;
            display: flex; align-items: center; justify-content: center;
        }
        .benefit-box:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 43, 89, 0.1);
            border-color: rgba(0, 157, 230, 0.5);
        }
        .benefit-box h6 {
            font-size: 1rem; font-weight: 600;
            color: #33475b; text-align: center; margin: 0;
        }

        /* --- Responsive Design --- */
        @media (max-width: 767.98px) {
            .demo-key-point { flex-basis: calc(50% - 1rem); }
        }
        @media (max-width: 575.98px) {
            .demo-key-point { flex-basis: 100%; }
            .time-display { display: none; }
        }
        .new-demo-section, .video-features-benefits-section, .benefits-section {
            background: transparent !important;
            color: #f1f5f9 !important;
        }
      `}</style>

      {/* === Page Content Starts Here === */}
      
      <section ref={heroRef} className="hero-section">
        <div className="pipeline-container" ref={pipelineContainerRef}>
            {/* 3D Talent Pipeline will be rendered here */}
        </div>
        <div className="hero-content animate-on-scroll">
            <h1 className="hero-title">Hire Smarter, Not Harder</h1>
            <p className="hero-subtitle">The intelligent Applicant Tracking System designed to automate your recruitment, find the best candidates, and build winning teams faster.</p>
            <a href="#demo" className="hero-cta">Request a Demo</a>
        </div>
      </section>

      <section className="new-demo-section" id="demo">
        <div className="container">
          <h2 style={{
            fontSize: "clamp(2.2rem, 5vw, 3rem)",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}>Live ATS Platform Demonstration</h2>
          
          <p className="animate-on-scroll text-white" style={{transitionDelay: '0.2s'}}>Discover how our ATS streamlines sourcing, scheduling, and reporting to boost your hiring efficiency. See real-world scenarios that transform your entire recruitment process.</p>
          <div className="demo-key-points-grid">
            {demoKeyPoints.map((point, index) => (
              <div key={index} className="demo-key-point animate-on-scroll" style={{transitionDelay: `${0.3 + index * 0.1}s`}}>
                <point.icon className="icon" />
                <span>{point.text}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      <section className="video-features-benefits-section">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-7 mb-4 mb-lg-0 animate-on-scroll">
              <div className="video-container">
                <video ref={videoRef} onTimeUpdate={handleTimeUpdate} onLoadedMetadata={handleLoadedMetadata} onCanPlay={handleCanPlay} onError={handleVideoError} onPlay={() => setIsPlaying(true)} onPause={() => setIsPlaying(false)} preload="metadata" playsInline>
                  <source src={ATSVideo} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
                {isVideoLoading && <div className="video-overlay"><div className="spinner-border text-primary" role="status"></div></div>}
                {videoError && <div className="video-overlay"><p className="text-white bg-danger p-3 rounded">{videoError}</p></div>}
                {!isPlaying && !isVideoLoading && !videoError && (
                  <div className="video-overlay" onClick={handlePlayPause}>
                    <div className="play-button"><FaPlay size={30} color="white" style={{marginLeft: '4px'}} /></div>
                  </div>
                )}
                <div className="video-controls d-flex align-items-center">
                    <button className="btn" onClick={handlePlayPause} aria-label={isPlaying ? 'Pause' : 'Play'}>{isPlaying ? <FaPause /> : <FaPlay />}</button>
                    <button className="btn" onClick={handleSkipBackward} aria-label="Skip Backward"><FaFastBackward /></button>
                    <button className="btn" onClick={handleSkipForward} aria-label="Skip Forward"><FaFastForward /></button>
                    <div className="progress-bar-container" onClick={handleProgressClick}>
                        <div className="progress-bar" style={{ width: `${(currentTime / duration) * 100}%` }}></div>
                    </div>
                    <div className="time-display">{formatTime(currentTime)} / {formatTime(duration)}</div>
                    <button className="btn" onClick={handleVolumeToggle} aria-label={isMuted ? 'Unmute' : 'Mute'}>{isMuted ? <FaVolumeOff /> : <FaVolumeUp />}</button>
                    <button className="btn" onClick={handleFullscreen} aria-label="Fullscreen"><FaExpand /></button>
                </div>
              </div>
            </div>
            <div className="col-lg-5">
              <div className="feature-list-container">
                {features.map((feature, index) => (
                  <div key={index} className="feature-card animate-on-scroll" style={{transitionDelay: `${0.2 + index * 0.1}s`}}>
                    <div className="feature-icon"><feature.icon /></div>
                    <div>
                      <h5>{feature.title}</h5>
                      <p>{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="benefits-section">
        <div className="container">
          <h2 style={{
            fontSize: "clamp(2.2rem, 5vw, 3rem)",
            fontWeight: "800",
            letterSpacing: "2.6px",
            marginBottom: "1rem",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}>Everything You Need to Hire Faster</h2>
          <div className="row">
            {benefits.map((benefit, index) => (
              <div key={index} className="col-md-6 col-lg-3 mb-4">
                <div className="benefit-box animate-on-scroll" style={{transitionDelay: `${0.2 + index * 0.05}s`}}>
                  <h6>{benefit}</h6>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

    </div>
  );
};

export default ATSDemo;