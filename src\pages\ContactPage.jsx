import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from "react-leaflet";
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import "@fortawesome/fontawesome-free/css/all.min.css";

// --- Import your local images ---
import banpic1 from '../Asserts/banpic1.webp';
import banpic2 from '../Asserts/banpic2.webp';
import banpic3 from '../Asserts/banpic3.webp';
import banpic4 from '../Asserts/banpic4.webp';

import hydpic1 from '../Asserts/hydpic1.webp';
import hydpic2 from '../Asserts/hydpic2.webp';
import hydpic3 from '../Asserts/hydpic3.webp';
import hydpic4 from '../Asserts/hydpic4.webp';


// --- Map Change Component ---
const ChangeMapView = ({ coords, zoom = 12 }) => {
  const map = useMap();
  useEffect(() => {
    if (coords) {
      map.flyTo(coords, zoom, {
        animate: true,
        duration: 1.5,
      });
    }
  }, [coords, zoom, map]);
  return null;
};

// --- Dynamic 4-Grid Image Carousel Component ---
const OfficeImageCarousel = ({ selectedOffice }) => {
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Image sets for each office
  const officeImages = {
    bangalore: [
      { id: 1, src: banpic1, alt: 'Bangalore Office View 1' },
      { id: 2, src: banpic2, alt: 'Bangalore Office View 2' },
      { id: 3, src: banpic3, alt: 'Bangalore Office View 3' },
      { id: 4, src: banpic4, alt: 'Bangalore Office View 4' }
    ],
    hyderabad: [
      { id: 1, src: 'https, alt: 'Hyderabad Office Exterior' },
      { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+2', alt: 'Hyderabad Office Interior' },
      { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+3', alt: 'Hyderabad Team Workspace' },
      { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+4', alt: 'Hyderabad Meeting Room' }
    ],
    vijayawada: [
      { id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+1', alt: 'Vijayawada Office Exterior' },
      { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+2', alt: 'Vijayawada Office Interior' },
      { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+3', alt: 'Vijayawada Team Workspace' },
      { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+4', alt: 'Vijayawada Meeting Room' }
    ],
    texas: [
      { id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+1', alt: 'Texas Office Exterior' },
      { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+2', alt: 'Texas Office Interior' },
      { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+3', alt: 'Texas Team Workspace' },
      { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+4', alt: 'Texas Meeting Room' }
    ],
    melbourne: [
      { id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+1', alt: 'Melbourne Office Exterior' },
      { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+2', alt: 'Melbourne Office Interior' },
      { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+3', alt: 'Melbourne Team Workspace' },
      { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+4', alt: 'Melbourne Meeting Room' }
    ],
    ontario: [
      { id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+1', alt: 'Ontario Office Exterior' },
      { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+2', alt: 'Ontario Office Interior' },
      { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+3', alt: 'Ontario Team Workspace' },
      { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+4', alt: 'Ontario Meeting Room' }
    ]
  };

  const currentImages = officeImages[selectedOffice] || officeImages.bangalore;

  useEffect(() => {
    setIsTransitioning(true);
    const timer = setTimeout(() => setIsTransitioning(false), 300);
    return () => clearTimeout(timer);
  }, [selectedOffice]);

  return (
    <div className="relative h-full">
      <div className={`transition-opacity duration-300 h-full ${isTransitioning ? 'opacity-50' : 'opacity-100'}`}>
        <div className="grid grid-cols-2 grid-rows-2 gap-2 h-full">
          {currentImages.map((image) => (
            <div
              key={`${selectedOffice}-${image.id}`}
              className="relative overflow-hidden rounded-lg bg-gradient-to-br from-makonis-primary/20 to-makonis-secondary/20 group"
            >
              <img
                src={image.src}
                alt={image.alt}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                onError={(e) => {
                  e.target.src = `data:image/svg+xml;base64,${btoa(`
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 300" width="400" height="300">
                                            <rect width="100%" height="100%" fill="#002956"/>
                                            <text x="50%" y="45%" text-anchor="middle" fill="#00a0e9" font-size="14" font-family="Arial">
                                                ${image.alt}
                                            </text>
                                            <text x="50%" y="60%" text-anchor="middle" fill="#ffffff" font-size="10" font-family="Arial">
                                                Image Not Found
                                            </text>
                                        </svg>
                                    `)}`;
                }}
              />
              <div className="absolute inset-0 bg-gradient-to-t from-makonis-primary/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-2 left-2 right-2">
                  <p className="text-white text-xs font-medium">{image.alt}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="absolute top-3 left-3 z-10">
        <div className="bg-makonis-gradient px-2 py-1 rounded-md shadow-lg">
          <span className="text-white font-medium text-xs capitalize">{selectedOffice} Office Gallery</span>
        </div>
      </div>
    </div>
  );
};


// --- Main Contact Page Component ---
const ContactPage = () => {
  const [activeTab, setActiveTab] = useState("bangalore");

  const locations = [
    // The unused 'directionsUrl' key has been removed
    { id: "bangalore", title: "Bangalore", fullTitle: "Bangalore Office", address: "51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037", phone: "+91 8041707838", email: "<EMAIL>", coords: [12.958057, 77.702026] },
    { id: "hyderabad", title: "Hyderabad", fullTitle: "Hyderabad Office", address: "5th Floor, Modern Profound Tech Park, HITEC City, Kondapur, Telangana 500081", phone: "+91 8041707838", email: "<EMAIL>", coords: [17.4593, 78.3659] },
    { id: "vijayawada", title: "Vijayawada", fullTitle: "Vijayawada Office", address: "71-3-8A, Koneru vari St, Patamata, Benz Circle, Vijayawada, Andhra Pradesh 520010", phone: "+91 8041707838", email: "<EMAIL>", coords: [16.4952, 80.6496] },
    { id: "texas", title: "Texas", fullTitle: "Texas Office", address: "Suite -410 Office – T Kings Plaza 14111 King Rd Frisco TX 75036", phone: "****** 525 8121", email: "<EMAIL>", coords: [33.1497, -96.7972] },
    { id: "melbourne", title: "Melbourne", fullTitle: "Melbourne Office", address: "54. Mansfield ST Berwick VIC 3806", phone: "+61 3 9707 1122", email: "<EMAIL>", coords: [-38.0322, 145.3473] },
    { id: "ontario", title: "Ontario", fullTitle: "Ontario Office", address: "4503 Glen Erin Dr., Mississauga, ON, Canada L5M 4G5", phone: "****** 828 2222", email: "<EMAIL>", coords: [43.5674, -79.7072] },
  ];

  const currentOffice = locations.find((loc) => loc.id === activeTab);

  const overviewMarkerIcon = new L.Icon({
    iconUrl: 'data:image/svg+xml;base64,' + btoa(`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18"><circle cx="12" cy="12" r="9" fill="#00a0e9" stroke="#ffffff" stroke-width="2"/></svg>`),
    iconSize: [18, 18],
    iconAnchor: [9, 9],
  });

  return (
    <div className="min-h-screen relative overflow-hidden" style={{ background: "linear-gradient(135deg, rgba(0, 41, 86, 0.95) 0%, rgba(0, 41, 86, 0.98) 100%)" }}>
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 opacity-20" style={{ backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`, backgroundSize: "50px 50px" }} />
      </div>
      <div className="container-makonis section-padding-sm relative z-10">
        <div className="text-center mb-8">
          <h1 style={{
            fontSize: "3.6rem",
            fontWeight: "800",
            letterSpacing: "2.6px",
            background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
          }}>Our Global Presence</h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed px-2">Connect with us at any of our strategically located offices around the world. We're here to serve you with excellence and innovation.</p>
        </div>

        <div className="mb-8 lg:mb-12 card-makonis-glass p-4">
          <MapContainer center={[25, 20]} zoom={2} style={{ height: '400px', width: '100%' }} className="rounded-xl" zoomControl={false} scrollWheelZoom={false} dragging={false} doubleClickZoom={false} attributionControl={false} touchZoom={false}>
            <TileLayer url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png" />
            {locations.map(loc => (
              <Marker key={`overview-${loc.id}`} position={loc.coords} icon={overviewMarkerIcon}>
                <Popup className="custom-popup"><div className="p-1"><h4 className="font-semibold text-white text-center">{loc.title}</h4></div></Popup>
              </Marker>
            ))}
          </MapContainer>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
          {/* Left Column: Image Gallery */}
          <div className="lg:col-span-8">
            <div className="card-makonis-glass p-4 lg:p-6 h-full flex flex-col">
              <div className="flex-grow">
                <OfficeImageCarousel selectedOffice={activeTab} />
              </div>
            </div>
          </div>

          {/* Right Column: Selector and Details */}
          <div className="lg:col-span-4 space-y-6 lg:space-y-8">
            {/* Location Selector Card */}
            <div className="card-makonis-glass p-3 lg:p-4">
              <h3 className="text-lg lg:text-xl font-semibold text-white mb-3 lg:mb-4">Select Office</h3>
              <div className="grid grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-2">
                {locations.map((location) => (
                  <button key={location.id} className={`px-2 py-1.5 lg:px-3 lg:py-2 rounded-lg font-medium transition-all duration-300 text-xs lg:text-sm ${activeTab === location.id ? 'bg-makonis-gradient text-white shadow-glow transform scale-105' : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white border border-white/20'}`} onClick={() => setActiveTab(location.id)}>
                    {location.title}
                  </button>
                ))}
              </div>
            </div>
            {/* Office Details Card */}
            {currentOffice && (
              <div className="card-makonis-glass p-3 lg:p-4 animate-fade-in">
                <div className="flex items-center gap-2 lg:gap-3 mb-3 lg:mb-2">
                  <div className="w-8 h-8 lg:w-10 lg:h-10 bg-makonis-gradient rounded-lg flex items-center justify-center"><i className="fas fa-building text-white text-xs lg:text-sm"></i></div>
                  <h3 className="text-base lg:text-lg font-semibold text-white">{currentOffice.fullTitle}</h3>
                </div>
                <div className="space-y-3 lg:space-y-4">
                  <div className="flex items-start gap-2 lg:gap-3">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0 mt-0.5"><i className="fas fa-map-marker-alt text-makonis-secondary text-xs lg:text-sm"></i></div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Address</span>
                      <p className="text-gray-300 mt-0.5 leading-relaxed text-xs lg:text-sm">{currentOffice.address}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2 lg:gap-3">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0"><i className="fas fa-phone text-makonis-secondary text-xs lg:text-sm"></i></div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Phone</span>
                      <a href={`tel:${currentOffice.phone}`} className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-0.5 text-xs lg:text-sm">{currentOffice.phone}</a>
                    </div>
                  </div>
                  <div className="flex items-start gap-2 lg:gap-3">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0"><i className="fas fa-envelope text-makonis-secondary text-xs lg:text-sm"></i></div>
                    <div>
                      <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Email</span>
                      <a href={`mailto:${currentOffice.email}`} className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-0.5 text-xs lg:text-sm">{currentOffice.email}</a>
                    </div>
                  </div>

                  {/* --- THIS IS THE FIX --- */}
                  <a
                    href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(currentOffice.address)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="btn-makonis-primary w-full text-center inline-flex items-center justify-center gap-2 mt-3 lg:mt-4 text-xs lg:text-sm py-2"
                  >
                    <i className="fas fa-directions text-xs lg:text-sm"></i>Get Directions
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <style>{`
                .leaflet-popup-content-wrapper, .leaflet-popup-tip {
                    background: rgba(0, 41, 86, 0.95) !important;
                    color: #fff !important;
                    border-radius: 12px !important;
                    border: 1px solid rgba(0, 160, 233, 0.3) !important;
                    backdrop-filter: blur(10px) !important;
                }
                .leaflet-popup-content { margin: 8px 12px !important; }
                .leaflet-popup-content a { color: #00a0e9 !important; }
                .leaflet-popup-close-button { color: #fff !important; font-size: 18px !important; padding: 4px 8px !important; }
                .leaflet-popup-close-button:hover { color: #00a0e9 !important; }
            `}</style>
    </div>
  );
};

export default ContactPage;