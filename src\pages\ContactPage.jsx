import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from "react-leaflet";
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import "@fortawesome/fontawesome-free/css/all.min.css";

// --- Import your local images ---
// For this example, we'll use placeholders. Replace them with your actual imports.
import banpic1 from '../Asserts/banpic1.webp';
import banpic2 from '../Asserts/banpic2.webp';
import banpic3 from '../Asserts/banpic3.webp';
import banpic4 from '../Asserts/banpic4.webp';
// Add more images for the carousel effect
// import banpic5 from '../Asserts/banpic5.webp';
// import banpic6 from '../Asserts/banpic6.webp';
// import banpic7 from '../Asserts/banpic7.webp';
// import banpic8 from '../Asserts/banpic8.webp';


// --- Map Back Button Component ---
// This component displays a button to reset the map view.
const MapBackButton = ({ isZoomed, onReset }) => {
    if (!isZoomed) return null;

    return (
        <div className="leaflet-top leaflet-left">
            <div className="leaflet-control leaflet-bar" style={{ margin: '12px' }}>
                <button 
                    onClick={onReset} 
                    title="Back to World View"
                    className="w-10 h-10 bg-white hover:bg-gray-100 flex items-center justify-center cursor-pointer shadow-md rounded-md"
                >
                    <i className="fas fa-globe-americas text-makonis-primary text-xl"></i>
                </button>
            </div>
        </div>
    );
};


// --- Map Controller Component ---
// This component handles all map animations and marker logic.
const MapController = ({ locations, activeTab, isZoomed, setActiveTabAndZoom }) => {
    const map = useMap();

    // Effect to fly to the selected office or back to the default view
    useEffect(() => {
        const defaultView = { coords: [25, 20], zoom: 2.5 };
        const activeLocation = locations.find(loc => loc.id === activeTab);

        if (isZoomed && activeLocation) {
            map.flyTo(activeLocation.coords, 14, { animate: true, duration: 1.5 });
        } else {
            map.flyTo(defaultView.coords, defaultView.zoom, { animate: true, duration: 1.5 });
        }
    }, [activeTab, isZoomed, locations, map]);

    // Custom Icons
    const createIcon = (color, isPulse = false) => new L.divIcon({
        className: 'custom-marker-icon',
        html: `<div class="marker-pin" style="background-color:${color};"></div>${isPulse ? '<div class="marker-pulse" style="border-color:${color};"></div>' : ''}`,
        iconSize: [30, 42],
        iconAnchor: [15, 42],
        popupAnchor: [0, -42],
    });

    const defaultIcon = createIcon("#00a0e9"); // Blue for default
    const activeIcon = createIcon("#ffc107", true); // Yellow with pulse for active

    return (
        <>
            {locations.map(loc => (
                <Marker
                    key={loc.id}
                    position={loc.coords}
                    icon={activeTab === loc.id ? activeIcon : defaultIcon}
                    eventHandlers={{
                        click: () => {
                            setActiveTabAndZoom(loc.id); // Clicking a marker makes it active and zooms
                        },
                    }}
                >
                    <Popup className="custom-popup">
                        <div className="p-1 text-center">
                            <h4 className="font-semibold text-white">{loc.title}</h4>
                            <p className="text-gray-300 text-xs">{loc.address.split(',').slice(0, 2).join(', ')}</p>
                            <button
                                onClick={() => setActiveTabAndZoom(loc.id)}
                                className="mt-2 px-3 py-1 bg-makonis-gradient text-white rounded-md text-xs font-bold hover:opacity-90"
                            >
                                View Details
                            </button>
                        </div>
                    </Popup>
                </Marker>
            ))}
        </>
    );
};


// --- Dynamic 4-Grid Image Carousel Component (No Dots) ---
const OfficeImageCarousel = ({ selectedOffice }) => {
    const [currentPage, setCurrentPage] = useState(0);

    // Each office has an array of "pages", where each page is an array of 4 images.
    const officeImagePages = {
        bangalore: [
            [{ id: 1, src: banpic1, alt: 'Bangalore Office View 1' }, { id: 2, src: banpic2, alt: 'Bangalore Office View 2' }, { id: 3, src: banpic3, alt: 'Bangalore Office View 3' }, { id: 4, src: banpic4, alt: 'Bangalore Office View 4' }],
            [{ id: 5, src: banpic5, alt: 'Bangalore Recreation Area' }, { id: 6, src: banpic6, alt: 'Bangalore Team Collaboration' }, { id: 7, src: banpic7, alt: 'Bangalore Workspace' }, { id: 8, src: banpic8, alt: 'Bangalore Entrance' }]
        ],
        // Add more image pages for other locations here
        hyderabad: [[{ id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+1', alt: 'Hyderabad Office Exterior' }, { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+2', alt: 'Hyderabad Office Interior' }, { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+3', alt: 'Hyderabad Workspace' }, { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+4', alt: 'Hyderabad Meeting Room' }]],
    };

    const currentImagePages = officeImagePages[selectedOffice] || officeImagePages.bangalore;
    const currentImages = currentImagePages[currentPage] || currentImagePages[0];

    // Reset carousel on office change
    useEffect(() => {
        setCurrentPage(0);
    }, [selectedOffice]);

    // Auto-play carousel
    useEffect(() => {
        if (currentImagePages.length > 1) {
            const timer = setInterval(() => {
                setCurrentPage(prevPage => (prevPage + 1) % currentImagePages.length);
            }, 5000); // Change image set every 5 seconds
            return () => clearInterval(timer);
        }
    }, [selectedOffice, currentImagePages.length]);

    return (
        <div className="relative h-full flex flex-col">
            <div className="absolute top-3 left-3 z-10">
                <div className="bg-makonis-gradient px-2 py-1 rounded-md shadow-lg">
                    <span className="text-white font-medium text-xs capitalize">{selectedOffice} Office Gallery</span>
                </div>
            </div>
            <div className="flex-grow grid grid-cols-2 grid-rows-2 gap-2">
                {currentImages.map((image) => (
                    <div key={`${selectedOffice}-${image.id}`} className="relative overflow-hidden rounded-lg group">
                        <img src={image.src} alt={image.alt} className="w-full h-full object-cover transition-all duration-500 group-hover:scale-105" onError={(e) => { e.target.src = 'https://via.placeholder.com/400x300/002956/FFFFFF?text=Image+Not+Found'; }}/>
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-2">
                            <p className="text-white text-xs font-medium">{image.alt}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};


// --- Main Contact Page Component ---
const ContactPage = () => {
    const [activeTab, setActiveTab] = useState("bangalore");
    const [isMapZoomed, setIsMapZoomed] = useState(false);

    const locations = [
        // Location data remains the same
        { id: "bangalore", title: "Bangalore", fullTitle: "Bangalore Office", address: "51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037", phone: "+91 8041707838", email: "<EMAIL>", coords: [12.958057, 77.702026] },
        { id: "hyderabad", title: "Hyderabad", fullTitle: "Hyderabad Office", address: "5th Floor, Modern Profound Tech Park, HITEC City, Kondapur, Telangana 500081", phone: "+91 8041707838", email: "<EMAIL>", coords: [17.4593, 78.3659] },
        // ...other locations
    ];

    const currentOffice = locations.find((loc) => loc.id === activeTab);
    
    // New handler to set tab and trigger zoom
    const handleSetTabAndZoom = (id) => {
        setActiveTab(id);
        setIsMapZoomed(true);
    };

    // New handler to reset the map view
    const handleResetMap = () => {
        setIsMapZoomed(false);
    };

    return (
        <div className="min-h-screen relative overflow-hidden bg-[#f0f4f8]">
            {/* ... Header and other elements remain the same ... */}
            <div className="container-makonis section-padding-sm relative z-10">
                {/* --- MAP CONTAINER (with new controls) --- */}
                <div className="mb-8 lg:mb-12 rounded-2xl shadow-xl overflow-hidden border-4 border-white">
                    <MapContainer
                        center={[25, 20]}
                        zoom={2.5}
                        style={{ height: '450px', width: '100%' }}
                        // All user interactions are disabled
                        zoomControl={false}
                        scrollWheelZoom={false}
                        dragging={false}
                        doubleClickZoom={false}
                        attributionControl={false}
                        touchZoom={false}
                    >
                        <TileLayer url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png" />
                        <MapController locations={locations} activeTab={activeTab} isZoomed={isMapZoomed} setActiveTabAndZoom={handleSetTabAndZoom} />
                        <MapBackButton isZoomed={isMapZoomed} onReset={handleResetMap} />
                    </MapContainer>
                </div>
                {/* ... Rest of the component (Image Gallery, Office Selector, Details) ... */}
            </div>

            <style>{`
                /* New Marker Styles */
                .marker-pin {
                    width: 30px;
                    height: 30px;
                    border-radius: 50% 50% 50% 0;
                    position: absolute;
                    transform: rotate(-45deg);
                    left: 50%;
                    top: 50%;
                    margin: -15px 0 0 -15px;
                    border: 2px solid white;
                }
                .marker-pin::after {
                    content: '';
                    width: 14px;
                    height: 14px;
                    margin: 6px 0 0 6px;
                    background: #ffffff;
                    position: absolute;
                    border-radius: 50%;
                }
                .marker-pulse {
                    background: transparent;
                    border-radius: 50%;
                    height: 30px;
                    width: 30px;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    margin: -15px 0 0 -15px;
                    transform: rotate(-45deg);
                    border: 5px solid;
                    animation: pulsate 1.5s ease-out infinite;
                    opacity: 0;
                }
                @keyframes pulsate {
                    0% { transform: rotate(-45deg) scale(0.1, 0.1); opacity: 0.0; }
                    50% { opacity: 1.0; }
                    100% { transform: rotate(-45deg) scale(1.2, 1.2); opacity: 0.0; }
                }
                /* Other styles remain the same */
            `}</style>
        </div>
    );
};

export default ContactPage;