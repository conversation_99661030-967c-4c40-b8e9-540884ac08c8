import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from "react-leaflet";
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import "@fortawesome/fontawesome-free/css/all.min.css";

// --- Import your local images ---
// NOTE: You will need to add more image imports for the other locations
// and update the `officeImagePages` object accordingly.
import banpic1 from '../Asserts/banpic1.webp';
import banpic2 from '../Asserts/banpic2.webp';
import banpic3 from '../Asserts/banpic3.webp';
import banpic4 from '../Asserts/banpic4.webp';
// import banpic5 from '../Asserts/banpic5.webp';
// import banpic6 from '../Asserts/banpic6.webp';
// import banpic7 from '../Asserts/banpic7.webp';
// import banpic8 from '../Asserts/banpic8.webp';

// --- Map Back Button Component ---
const MapBackButton = ({ isZoomed, onReset }) => {
    if (!isZoomed) return null;

    return (
        <div className="leaflet-top leaflet-left">
            <div className="leaflet-control leaflet-bar" style={{ margin: '12px' }}>
                <button
                    onClick={onReset}
                    title="Back to World View"
                    className="w-10 h-10 bg-white hover:bg-gray-100 flex items-center justify-center cursor-pointer shadow-md rounded-md"
                >
                    <i className="fas fa-globe-americas text-makonis-primary text-xl"></i>
                </button>
            </div>
        </div>
    );
};

// --- Map Controller Component ---
const MapController = ({ locations, activeTab, isZoomed, setActiveTabAndZoom }) => {
    const map = useMap();

    useEffect(() => {
        const defaultView = { coords: [25, 20], zoom: 2.5 };
        const activeLocation = locations.find(loc => loc.id === activeTab);

        if (isZoomed && activeLocation) {
            map.flyTo(activeLocation.coords, 14, { animate: true, duration: 1.5 });
        } else {
            map.flyTo(defaultView.coords, defaultView.zoom, { animate: true, duration: 1.5 });
        }
    }, [activeTab, isZoomed, locations, map]);

    const createIcon = (color, isPulse = false) => new L.divIcon({
        className: 'custom-marker-icon',
        html: `<div class="marker-pin" style="background-color:${color};"></div>${isPulse ? '<div class="marker-pulse" style="border-color:${color};"></div>' : ''}`,
        iconSize: [30, 42],
        iconAnchor: [15, 42],
        popupAnchor: [0, -42],
    });

    const defaultIcon = createIcon("#00a0e9");
    const activeIcon = createIcon("#ffc107", true);

    return (
        <>
            {locations.map(loc => (
                <Marker
                    key={loc.id}
                    position={loc.coords}
                    icon={activeTab === loc.id ? activeIcon : defaultIcon}
                    eventHandlers={{
                        click: () => {
                            setActiveTabAndZoom(loc.id);
                        },
                    }}
                >
                    <Popup className="custom-popup">
                        <div className="p-1 text-center">
                            <h4 className="font-semibold text-white">{loc.title}</h4>
                            <p className="text-gray-300 text-xs">{loc.address.split(',').slice(0, 2).join(', ')}</p>
                            <button
                                onClick={() => setActiveTabAndZoom(loc.id)}
                                className="mt-2 px-3 py-1 bg-makonis-gradient text-white rounded-md text-xs font-bold hover:opacity-90"
                            >
                                View Details
                            </button>
                        </div>
                    </Popup>
                </Marker>
            ))}
        </>
    );
};

// --- Dynamic 4-Grid Image Carousel Component ---
const OfficeImageCarousel = ({ selectedOffice }) => {
    const [currentPage, setCurrentPage] = useState(0);

    const officeImagePages = {
        bangalore: [
            [{ id: 1, src: banpic1, alt: 'Bangalore Office View 1' }, { id: 2, src: banpic2, alt: 'Bangalore Office View 2' }, { id: 3, src: banpic3, alt: 'Bangalore Office View 3' }, { id: 4, src: banpic4, alt: 'Bangalore Office View 4' }],
            [{ id: 5, src: banpic5, alt: 'Bangalore Recreation Area' }, { id: 6, src: banpic6, alt: 'Bangalore Team Collaboration' }, { id: 7, src: banpic7, alt: 'Bangalore Workspace' }, { id: 8, src: banpic8, alt: 'Bangalore Entrance' }]
        ],
        hyderabad: [
            [{ id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+1', alt: 'Hyderabad Office Exterior' }, { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+2', alt: 'Hyderabad Office Interior' }, { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+3', alt: 'Hyderabad Workspace' }, { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Hyderabad+4', alt: 'Hyderabad Meeting Room' }]
        ],
        vijayawada: [
            [{ id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+1', alt: 'Vijayawada Office Exterior' }, { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+2', alt: 'Vijayawada Office Interior' }, { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+3', alt: 'Vijayawada Workspace' }, { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Vijayawada+4', alt: 'Vijayawada Meeting Room' }]
        ],
        texas: [
            [{ id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+1', alt: 'Texas Office Exterior' }, { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+2', alt: 'Texas Office Interior' }, { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+3', alt: 'Texas Workspace' }, { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Texas+4', alt: 'Texas Meeting Room' }]
        ],
        melbourne: [
            [{ id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+1', alt: 'Melbourne Office Exterior' }, { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+2', alt: 'Melbourne Office Interior' }, { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+3', alt: 'Melbourne Workspace' }, { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Melbourne+4', alt: 'Melbourne Meeting Room' }]
        ],
        ontario: [
            [{ id: 1, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+1', alt: 'Ontario Office Exterior' }, { id: 2, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+2', alt: 'Ontario Office Interior' }, { id: 3, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+3', alt: 'Ontario Workspace' }, { id: 4, src: 'https://via.placeholder.com/400x300/003566/FFFFFF?text=Ontario+4', alt: 'Ontario Meeting Room' }]
        ]
    };

    const currentImagePages = officeImagePages[selectedOffice] || officeImagePages.bangalore;
    const currentImages = currentImagePages[currentPage] || currentImagePages[0];

    useEffect(() => {
        setCurrentPage(0);
    }, [selectedOffice]);

    useEffect(() => {
        if (currentImagePages.length > 1) {
            const timer = setInterval(() => {
                setCurrentPage(prevPage => (prevPage + 1) % currentImagePages.length);
            }, 5000);
            return () => clearInterval(timer);
        }
    }, [selectedOffice, currentImagePages.length]);

    return (
        <div className="relative h-full flex flex-col">
            <div className="absolute top-3 left-3 z-10">
                <div className="bg-makonis-gradient px-2 py-1 rounded-md shadow-lg">
                    <span className="text-white font-medium text-xs capitalize">{selectedOffice} Office Gallery</span>
                </div>
            </div>
            <div className="flex-grow grid grid-cols-2 grid-rows-2 gap-2">
                {currentImages.map((image) => (
                    <div key={`${selectedOffice}-${image.id}`} className="relative overflow-hidden rounded-lg group">
                        <img src={image.src} alt={image.alt} className="w-full h-full object-cover transition-all duration-500 group-hover:scale-105" onError={(e) => { e.target.src = 'https://via.placeholder.com/400x300/002956/FFFFFF?text=Image+Not+Found'; }} />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-2">
                            <p className="text-white text-xs font-medium">{image.alt}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

// --- Main Contact Page Component ---
const ContactPage = () => {
    const [activeTab, setActiveTab] = useState("bangalore");
    const [isMapZoomed, setIsMapZoomed] = useState(false);

    const locations = [
        { id: "bangalore", title: "Bangalore", fullTitle: "Bangalore Office", address: "51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037", phone: "+91 8041707838", email: "<EMAIL>", coords: [12.958057, 77.702026] },
        { id: "hyderabad", title: "Hyderabad", fullTitle: "Hyderabad Office", address: "5th Floor, Modern Profound Tech Park, HITEC City, Kondapur, Telangana 500081", phone: "+91 8041707838", email: "<EMAIL>", coords: [17.4593, 78.3659] },
        { id: "vijayawada", title: "Vijayawada", fullTitle: "Vijayawada Office", address: "71-3-8A, Koneru vari St, Patamata, Benz Circle, Vijayawada, Andhra Pradesh 520010", phone: "+91 8041707838", email: "<EMAIL>", coords: [16.4952, 80.6496] },
        { id: "texas", title: "Texas", fullTitle: "Texas Office", address: "Suite -410 Office – T Kings Plaza 14111 King Rd Frisco TX 75036", phone: "****** 525 8121", email: "<EMAIL>", coords: [33.1497, -96.7972] },
        { id: "melbourne", title: "Melbourne", fullTitle: "Melbourne Office", address: "54. Mansfield ST Berwick VIC 3806", phone: "+61 3 9707 1122", email: "<EMAIL>", coords: [-38.0322, 145.3473] },
        { id: "ontario", title: "Ontario", fullTitle: "Ontario Office", address: "4503 Glen Erin Dr., Mississauga, ON, Canada L5M 4G5", phone: "****** 828 2222", email: "<EMAIL>", coords: [43.5674, -79.7072] },
    ];

    const currentOffice = locations.find((loc) => loc.id === activeTab);

    const handleSetTabAndZoom = (id) => {
        setActiveTab(id);
        setIsMapZoomed(true);
    };

    const handleResetMap = () => {
        setIsMapZoomed(false);
    };

    return (
        <div className="min-h-screen relative overflow-x-hidden bg-[#f0f4f8]">
            <div className="absolute inset-0 z-0 opacity-20" style={{ backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.05) 1px, transparent 1px)`, backgroundSize: "50px 50px" }} />
            <div className="container-makonis section-padding-sm relative z-10">
                <div className="text-center mb-8">
                    <h1 style={{
                        fontSize: "3.6rem",
                        fontWeight: "800",
                        letterSpacing: "2.6px",
                        color: '#002956',
                        textShadow: '0 0 30px rgba(0, 160, 233, 0.1)'
                    }}>Our Global Presence</h1>
                    <p className="text-base sm:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed px-2">Connect with us at any of our strategically located offices around the world. We're here to serve you.</p>
                </div>

                <div className="mb-8 lg:mb-12 rounded-2xl shadow-xl overflow-hidden border-4 border-white">
                    <MapContainer
                        center={[25, 20]}
                        zoom={2.5}
                        style={{ height: '450px', width: '100%' }}
                        zoomControl={false}
                        scrollWheelZoom={false}
                        dragging={false}
                        doubleClickZoom={false}
                        attributionControl={false}
                        touchZoom={false}
                    >
                        <TileLayer url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png" />
                        <MapController locations={locations} activeTab={activeTab} isZoomed={isMapZoomed} setActiveTabAndZoom={handleSetTabAndZoom} />
                        <MapBackButton isZoomed={isMapZoomed} onReset={handleResetMap} />
                    </MapContainer>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
                    <div className="lg:col-span-8">
                        <div className="card-makonis-glass p-2 h-full">
                            <OfficeImageCarousel selectedOffice={activeTab} />
                        </div>
                    </div>
                    <div className="lg:col-span-4 space-y-6 lg:space-y-8">
                        <div className="card-makonis-glass p-4">
                            <h3 className="text-lg font-semibold text-white mb-3">Select Office</h3>
                            <div className="grid grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-2">
                                {locations.map((location) => (
                                    <button key={location.id} className={`px-3 py-2 rounded-lg font-medium transition-all duration-300 text-sm ${activeTab === location.id ? 'bg-makonis-gradient text-white shadow-glow transform scale-105' : 'bg-white/10 text-gray-300 hover:bg-white/20 hover:text-white'}`} onClick={() => handleSetTabAndZoom(location.id)}>
                                        {location.title}
                                    </button>
                                ))}
                            </div>
                        </div>
                        {currentOffice && (
                            <div className="card-makonis-glass p-4 animate-fade-in">
                                <div className="flex items-center gap-3 mb-3">
                                    <div className="w-10 h-10 bg-makonis-gradient rounded-lg flex items-center justify-center"><i className="fas fa-building text-white"></i></div>
                                    <h3 className="text-lg font-semibold text-white">{currentOffice.fullTitle}</h3>
                                </div>
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3">
                                        <div className="w-8 h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0 mt-0.5"><i className="fas fa-map-marker-alt text-makonis-secondary"></i></div>
                                        <div>
                                            <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Address</span>
                                            <p className="text-gray-300 mt-0.5 leading-relaxed text-sm">{currentOffice.address}</p>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <div className="w-8 h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0"><i className="fas fa-phone text-makonis-secondary"></i></div>
                                        <div>
                                            <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Phone</span>
                                            <a href={`tel:${currentOffice.phone}`} className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-0.5 text-sm">{currentOffice.phone}</a>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3">
                                        <div className="w-8 h-8 bg-makonis-secondary/20 rounded-md flex items-center justify-center flex-shrink-0"><i className="fas fa-envelope text-makonis-secondary"></i></div>
                                        <div>
                                            <span className="text-xs font-medium text-makonis-secondary uppercase tracking-wide">Email</span>
                                            <a href={`mailto:${currentOffice.email}`} className="block text-white hover:text-makonis-secondary transition-colors duration-300 mt-0.5 text-sm">{currentOffice.email}</a>
                                        </div>
                                    </div>
                                    <a href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(currentOffice.address)}`} target="_blank" rel="noopener noreferrer" className="btn-makonis-primary w-full text-center inline-flex items-center justify-center gap-2 mt-4 text-sm py-2">
                                        <i className="fas fa-directions"></i>Get Directions
                                    </a>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <style>{`
                .leaflet-popup-content-wrapper, .leaflet-popup-tip {
                    background: rgba(0, 41, 86, 0.9) !important;
                    color: #fff !important;
                    border-radius: 8px !important;
                    border: 1px solid rgba(0, 160, 233, 0.3) !important;
                    backdrop-filter: blur(10px) !important;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.4) !important;
                }
                .leaflet-popup-content { margin: 0 !important; padding: 0 !important; }
                .leaflet-popup-close-button { color: #fff !important; }

                .card-makonis-glass {
                    background: rgba(0, 41, 86, 0.85);
                    backdrop-filter: blur(15px);
                    border: 1px solid rgba(0, 160, 233, 0.2);
                    border-radius: 1rem;
                    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
                }
                .bg-makonis-gradient { background-image: linear-gradient(135deg, #00a0e9 0%, #005f8d 100%); }
                .text-makonis-primary { color: #005f8d; }
                .text-makonis-secondary { color: #00a0e9; }
                .bg-makonis-secondary\\/20 { background-color: rgba(0, 160, 233, 0.2); }
                .btn-makonis-primary {
                    background-image: linear-gradient(135deg, #00a0e9 0%, #005f8d 100%);
                    color: white;
                    border-radius: 0.5rem;
                    transition: all 0.3s;
                }
                .btn-makonis-primary:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0, 160, 233, 0.4); }
                .shadow-glow { box-shadow: 0 0 15px rgba(0, 160, 233, 0.5); }
                
                .animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(10px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                .marker-pin {
                    width: 30px;
                    height: 30px;
                    border-radius: 50% 50% 50% 0;
                    position: absolute;
                    transform: rotate(-45deg);
                    left: 50%;
                    top: 50%;
                    margin: -15px 0 0 -15px;
                    border: 2px solid white;
                    box-shadow: 0 3px 10px rgba(0,0,0,0.3);
                }
                .marker-pin::after {
                    content: '';
                    width: 14px;
                    height: 14px;
                    margin: 6px 0 0 6px;
                    background: #ffffff;
                    position: absolute;
                    border-radius: 50%;
                }
                .marker-pulse {
                    background: transparent;
                    border-radius: 50%;
                    height: 30px;
                    width: 30px;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    margin: -15px 0 0 -15px;
                    transform: rotate(-45deg);
                    border: 5px solid;
                    animation: pulsate 1.5s ease-out infinite;
                    opacity: 0;
                }
                @keyframes pulsate {
                    0% { transform: rotate(-45deg) scale(0.1, 0.1); opacity: 0.0; }
                    50% { opacity: 1.0; }
                    100% { transform: rotate(-45deg) scale(1.2, 1.2); opacity: 0.0; }
                }
           `}</style>
        </div>
    );
};

export default ContactPage;