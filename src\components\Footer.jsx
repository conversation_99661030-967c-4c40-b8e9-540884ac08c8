import React from 'react';
import { Link } from 'react-router-dom';

// IMPORTS YOUR LOGO:
// Assuming Footer.js is in 'src/components' and your logo is in 'src/Asserts'
import MakonisLogo from "../Asserts/Makonis-Logo.png";

const Footer = () => {
  return (
    <footer className="text-gray-800 py-12 lg:py-16 text-sm bg-white shadow-lg"> {/* Changed background to white, adjusted text color */}
      <div className="container mx-auto px-4"> {/* Changed container-makonis to mx-auto px-4 for standard Tailwind */}

        {/* Main Footer Content: Logo, About, Services, Contact */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 mb-12 pb-8 border-b border-gray-300"> {/* Adjusted border color */}
          <div className="lg:col-span-4 xl:col-span-3 pr-0 lg:pr-8">
            <div className="px-4 py-3 rounded inline-block mb-4 ml-10"> {/* Removed bg-white from here */}
              <Link to="/" className="text-decoration-none">
                <img
                  src={MakonisLogo}
                  alt="Makonis Software Logo"
                  className="h-30 w-auto block" // Logo size increased here to h-20
                />
              </Link>
            </div>
          </div>

          <div className="lg:col-span-8 xl:col-span-9">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8">
              {/* About Us Links */}
              <div>
                <h5 className="text-gray-900 font-semibold mb-4 tracking-wide">About Us</h5> {/* Adjusted text color */}
                <ul className="space-y-2">
                  {[
                    { label: 'Company', href: '/#' },
                    { label: 'Our Team', href: '/#' },
                    { label: 'Careers', href: '/#' },
                    { label: 'Blog', href: '/#' }
                  ].map(link => (
                    <li key={link.label}>
                      <Link to={link.href} className="text-gray-700 hover:text-[#00AEEF] transition-colors duration-300 group flex items-center"> {/* Adjusted link colors */}
                        <i className="fas fa-chevron-right text-xs text-gray-500 mr-2 transition-transform duration-200 group-hover:translate-x-1 group-hover:text-[#00AEEF]"></i>
                        <span>{link.label}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Services Links */}
              <div>
                <h5 className="text-gray-900 font-semibold mb-4 tracking-wide">Services</h5> {/* Adjusted text color */}
                <ul className="space-y-2">
                  {[
                    { label: 'Artificial Intelligence', to: '/ai', icon: 'fas fa-robot' },
                    { label: 'Data Analytics', to: '/analytics', icon: 'fas fa-chart-bar' },
                    { label: 'IoT Solutions', to: '/iot', icon: 'fas fa-wifi' },
                    { label: 'Web & Mobile Dev', to: '/webdev', icon: 'fas fa-mobile-alt' },
                    { label: 'Testing Services', to: '/testing', icon: 'fas fa-bug' },
                    { label: 'Embedded Systems', to: '/embedded', icon: 'fas fa-microchip' },
                  ].map(service => (
                    <li key={service.label}>
                      <Link to={service.to} className="text-gray-700 hover:text-[#00AEEF] transition-colors duration-300 group flex items-center"> {/* Adjusted link colors */}
                        <i className={`text-xs text-[#00AEEF] mr-2 ${service.icon}`}></i> {/* Added icon and styled it */}
                        <span>{service.label}</span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Contact Us Information */}
              <div>
                <h5 className="text-gray-900 font-semibold mb-4 tracking-wide">Contact Us</h5> {/* Adjusted text color */}
                <ul className="space-y-3">
                  <li className="flex">
                    <i className="fas fa-map-marker-alt text-gray-500 mt-1 mr-3 flex-shrink-0 w-4"></i> {/* Adjusted icon color */}
                    <span className="text-gray-600">51, 3rd Cross Rd, Aswath Nagar, Marathahalli, Bengaluru, Karnataka 560037</span> {/* Adjusted text color */}
                  </li>
                  <li className="flex">
                    <i className="fas fa-envelope text-gray-500 mt-1 mr-3 flex-shrink-0 w-4"></i> {/* Adjusted icon color */}
                    <a href="mailto:<EMAIL>" className="text-gray-700 hover:text-[#00AEEF] transition-colors duration-300"><EMAIL></a> {/* Adjusted link colors */}
                  </li>
                  <li className="flex">
                    <i className="fas fa-phone-alt text-gray-500 mt-1 mr-3 flex-shrink-0 w-4"></i> {/* Adjusted icon color */}
                    <a href="tel:+918041707838" className="text-gray-700 hover:text-[#00AEEF] transition-colors duration-300">+91 8041707838</a> {/* Adjusted link colors */}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section: Copyright and Social Icons */}
        <div className="flex flex-col md:flex-row items-center justify-between pt-0">
          <div className="text-center md:text-left mb-4 md:mb-0">
            <p className="text-gray-600 text-sm"> {/* Adjusted text color */}
              &copy; {new Date().getFullYear()} Makonis Software Solutions. All Rights Reserved.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {[
              { label: 'Facebook', href: '#', icon: 'fab fa-facebook-f' },
              { label: 'Twitter', href: '#', icon: 'fab fa-twitter' },
              { label: 'LinkedIn', href: '#', icon: 'fab fa-linkedin-in' },
              { label: 'Instagram', href: '#', icon: 'fab fa-instagram' }
            ].map(social => (
              <a
                href={social.href}
                className="group" // Simplified class, hover effects defined on inner div
                key={social.label}
                aria-label={social.label}
                target="_blank"
                rel="noopener noreferrer"
              >
                <div className="w-10 h-10 rounded-full bg-gray-200 border border-gray-300 flex items-center justify-center transition-all duration-300 group-hover:bg-[#00AEEF] group-hover:border-[#00AEEF] group-hover:-translate-y-1 group-hover:scale-105 group-hover:shadow-lg"> {/* Adjusted background, border, and shadow for white background */}
                  <i className={`${social.icon} text-gray-600 transition-colors duration-300 group-hover:text-white`}></i> {/* Adjusted icon color and hover color */}
                </div>
              </a>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;