import React, { useEffect, useRef } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import ServicesSection from '../components/ServicesSection';
import ClientsSection from '../components/ClientsSection';
import QuickTestingSection from '../components/QuickTestingSection';
import homePagevideo1 from '../Asserts/homepagevideo1.mp4';
import homePagevideo2 from '../Asserts/homepagevideo2.mp4';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

const HomePage = () => {
    // Refs for animation targets
    const heroRef = useRef(null);
    const mainTitleRef = useRef(null);
    const subtitleRef = useRef(null);

    // Hero section animations with slide-in effects
    useEffect(() => {
        const ctx = gsap.context(() => {
            const tl = gsap.timeline({ delay: 0.2 });

            // Main title slides in from top
            if (mainTitleRef.current) {
                tl.from(mainTitleRef.current, {
                    y: -100,
                    opacity: 0,
                    duration: 1.2,
                    ease: "power3.out"
                });
            }

            // Subtitle slides in from bottom
            if (subtitleRef.current) {
                tl.from(subtitleRef.current, {
                    y: 100,
                    opacity: 0,
                    duration: 1.2,
                    ease: "power2.out"
                }, "-=0.8"); // Overlap with previous animation
            }
        }, heroRef);

        return () => ctx.revert();
    }, []);

    return (
        <>
            {/* Stunning Hero Section */}
            <section>
                <style>{`
                    /* --- General Body/Page Styles --- */
                    .ats-demo-page {
                        overflow-x: hidden;
                    }

                    /* --- Hero Section Styles --- */
                    .hero-section {
                        position: relative;
                        background-size: cover;
                        background-position: center;
                    }

                    .hero-section::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
                        opacity: 0.3;
                        pointer-events: none;
                    }
                    
                    /* --- Other sections --- */
                    .services-section, .clients-section, .quick-testing-section {
                        padding: 4rem 0; /* Example padding */
                    }
                `}</style>
                <section
                    ref={heroRef}
                    className="position-relative overflow-hidden"
                    style={{
                        minHeight: '85vh',
                        display: 'flex',
                        alignItems: 'center',
                    }}
                >
                    {/* Video Background */}
                    <div className="position-absolute w-100 h-100" style={{ zIndex: -1 }}>
                        <video
                            autoPlay
                            muted
                            loop
                            playsInline
                            className="w-100 h-100"
                            style={{
                                objectFit: 'cover',
                                objectPosition: 'center'
                            }}
                        >
                            <source src={homePagevideo2} type="video/mp4" />
                            {/* Fallback for browsers that don't support video */}
                            Your browser does not support the video tag.
                        </video>
                    </div>

                    {/* Video Overlay for Text Readability */}
                    <div
                        className="position-absolute w-100 h-100"
                        style={{
                            zIndex: 1,
                            backgroundColor: 'rgba(0, 0, 0, 0.5)', // Darker overlay
                            backdropFilter: 'blur(0px)' // Optional: adds a subtle blur
                        }}
                    />

                    <div className="container position-relative" style={{ zIndex: 1 }}>
                        <div className="row align-items-center min-vh-100 justify-content-center">
                            <div className="col-lg-10 col-xl-8 text-center">
                                {/* Main Heading */}
                                <h1
                                    ref={mainTitleRef}
                                    className="display-2 fw-bold mb-4"
                                    style={{
                                        fontSize: 'clamp(2.5rem, 8vw, 3.5rem)',
                                        lineHeight: '1.2',
                                        color: '#ffffff',
                                        // textShadow: '0 0 15px rgba(0, 0, 0, 0.7), 0 0 30px rgba(0, 0, 0, 0.5)',
                                    }}
                                >
                                    Transforming Ideas Into <span style={{ color: '#00a0e9' }}>Innovative Software</span>
                                </h1>

                                {/* Paragraph/Subtitle */}
                                <p
                                    ref={subtitleRef}
                                    className="lead mb-5"
                                    style={{
                                        fontSize: 'clamp(1rem, 2.5vw, 1.3rem)',
                                        lineHeight: '1.6',
                                        color: 'rgba(255, 255, 255, 0.9)',
                                        maxWidth: '800px',
                                        margin: '0 auto', // Center the paragraph
                                        textShadow: '0 0 10px rgba(0, 0, 0, 0.8)'
                                    }}
                                >
                                    From AI to IoT, we deliver smart technology solutions that help businesses grow, evolve, and succeed.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>
            </section>
            
            <ServicesSection />
            <ClientsSection />
            <QuickTestingSection />
        </>
    );
};

export default HomePage;