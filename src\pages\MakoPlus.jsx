import React, { useRef, useState, useEffect } from 'react';
import {
    FaPlay, FaPause, FaVolumeUp, FaVolumeOff, FaExpand,
    FaChartLine, FaClock, FaRocket, FaEnvelope,
    FaFastBackward, FaFastForward, FaFileMedical, FaChartBar, FaShieldAlt,
    FaMobileAlt, FaMagic, FaStethoscope, FaChartPie, FaLock, FaFileAlt, FaUserMd, FaHeartbeat, FaFlask, FaPills, FaNotesMedical,
} from 'react-icons/fa';
import EMRVideo from '../Asserts/Makoplus.mp4'; // Make sure this path is correct
import { Container, Row, Col, Button, ProgressBar } from 'react-bootstrap';

const EMRDemo = () => {
    // Refs for video player and hero section elements
    const videoRef = useRef(null);
    const heroRef = useRef(null);
    const titleRef = useRef(null);
    const subtitleRef = useRef(null);
    const ctaRef = useRef(null);
    const visualizationRef = useRef(null);

    // State for video player
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [isMuted, setIsMuted] = useState(false);
    const [isVideoLoading, setIsVideoLoading] = useState(true);
    const [videoError, setVideoError] = useState(null);

    const features = [
        {
            icon: FaFileMedical,
            title: "Comprehensive Patient Records",
            description: "Centralized, secure access to full patient medical history."
        },
        {
            icon: FaChartBar,
            title: "Clinical Decision Support",
            description: "Built-in alerts and guidelines for better clinical decisions."
        },
        {
            icon: FaShieldAlt,
            title: "Robust Security & Compliance",
            description: "HIPAA- and GDPR-compliant, enterprise-grade data protection."
        },
        {
            icon: FaMobileAlt,
            title: "Mobile Accessibility",
            description: "Secure on-the-go access for clinicians and staff."
        }
    ];

    const demoKeyPoints = [
        { icon: FaMagic, text: "Streamlined Workflows" },
        { icon: FaStethoscope, text: "Comprehensive Patient Records" },
        { icon: FaChartPie, text: "Clinical Decision Support" },
        { icon: FaLock, text: "Robust Security & Compliance" }
    ];

    const benefits = [
        "Automated Clinical Documentation",
        "Integrated Telehealth & Virtual Consults",
        "Customizable Clinical Workflows",
        "Real-time Care Team Collaboration",
        "Mobile Access for Clinicians",
        "Seamless System Integrations",
        "Enhanced Data Analytics & Reporting",
        "Improved Data Security & Compliance"
    ];

    const emrNodes = [
        { icon: FaFileAlt, label: "Patient History", size: 60, angle: 0, distance: 180 },
        { icon: FaUserMd, label: "Clinician Notes", size: 70, angle: 60, distance: 220 },
        { icon: FaHeartbeat, label: "Vitals", size: 65, angle: 120, distance: 190 },
        { icon: FaFlask, label: "Lab Results", size: 75, angle: 180, distance: 230 },
        { icon: FaPills, label: "Medications", size: 60, angle: 240, distance: 200 },
        { icon: FaNotesMedical, label: "Treatment Plans", size: 70, angle: 300, distance: 210 },
    ];

    // All video player handlers (handlePlayPause, etc.) remain the same
    const handlePlayPause = () => {
        if (videoRef.current) {
            if (isPlaying) videoRef.current.pause();
            else videoRef.current.play();
            setIsPlaying(!isPlaying);
        }
    };
    const handleTimeUpdate = () => videoRef.current && setCurrentTime(videoRef.current.currentTime);
    const handleLoadedMetadata = () => {
        if (videoRef.current) {
            setDuration(videoRef.current.duration);
            setIsVideoLoading(false);
        }
    };
    const handleProgressClick = (e) => {
        if (videoRef.current) {
            const progressBar = e.currentTarget;
            const clickX = e.nativeEvent.offsetX;
            const newTime = (clickX / progressBar.offsetWidth) * duration;
            videoRef.current.currentTime = newTime;
            setCurrentTime(newTime);
        }
    };
    const formatTime = (time) => {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };
    const handleVolumeToggle = () => {
        if (videoRef.current) {
            videoRef.current.muted = !videoRef.current.muted;
            setIsMuted(videoRef.current.muted);
        }
    };
    const handleFullscreen = () => {
        if (videoRef.current) {
            if (videoRef.current.requestFullscreen) videoRef.current.requestFullscreen();
            else if (videoRef.current.mozRequestFullScreen) videoRef.current.mozRequestFullScreen();
            else if (videoRef.current.webkitRequestFullscreen) videoRef.current.webkitRequestFullscreen();
            else if (videoRef.current.msRequestFullscreen) videoRef.current.msRequestFullscreen();
        }
    };
    const handleSkipForward = () => videoRef.current && (videoRef.current.currentTime += 15);
    const handleSkipBackward = () => videoRef.current && (videoRef.current.currentTime -= 15);
    const handleCanPlay = () => { setIsVideoLoading(false); setVideoError(null); };
    const handleVideoError = () => { setVideoError('Error loading video.'); setIsVideoLoading(false); };

    // All useEffect hooks remain the same
    useEffect(() => {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        if (titleRef.current) titleRef.current.style.animation = 'fadeInUp 1s ease-out both';
                        if (subtitleRef.current) subtitleRef.current.style.animation = 'fadeInUp 1s ease-out 0.2s both';
                        if (ctaRef.current) ctaRef.current.style.animation = 'fadeInUp 1s ease-out 0.4s both';
                        if (visualizationRef.current) visualizationRef.current.style.animation = 'fadeInUp 1s ease-out 0.6s both';
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.1 }
        );
        if (heroRef.current) observer.observe(heroRef.current);
        return () => heroRef.current && observer.unobserve(heroRef.current);
    }, []);

    useEffect(() => {
        const elementsToAnimate = document.querySelectorAll('.video-features-benefits-section .col-lg-8, .feature-card, .new-demo-section h2, .new-demo-section p, .demo-key-point, .benefit-box');
        const sectionObserver = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeInUp 0.8s ease-out both';
                        // Staggering logic can be simplified or kept as is
                        sectionObserver.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.15 }
        );
        elementsToAnimate.forEach((el) => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(30px)';
            sectionObserver.observe(el);
        });
        return () => elementsToAnimate.forEach((el) => sectionObserver.unobserve(el));
    }, []);

    return (
        <div className="mako-plus-page" style={{ background: '#012855', color: '#f1f5f9', minHeight: '100vh' }}>
            <style jsx>{`
        /* Basic Page Wrapper and Global Resets */
        .emr-page-wrapper {
            overflow-x: hidden;
            font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            color: #001A3A;
            background: #001A3A; /* Fallback background */
        }

        /* General Animations */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* === NEW HERO SECTION STYLES === */
        .hero-section {
            position: relative;
            min-height: 100vh;
            background: linear-gradient(135deg, #001a3a 0%, #002b59 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            padding: 80px 1rem;
            text-align: center;
        }
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background-image:
              radial-gradient(circle at 20% 20%, rgba(0, 160, 233, 0.15) 0%, transparent 40%),
              radial-gradient(circle at 80% 70%, rgba(0, 86, 179, 0.15) 0%, transparent 40%);
            opacity: 0.8;
            z-index: 1;
        }
        .hero-content {
            position: relative;
            z-index: 3;
            max-width: 800px;
            margin: 0 auto;
        }
        .hero-title {
            font-size: clamp(2.8rem, 6vw, 4.5rem);
            font-weight: 800;
            background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 160, 233, 0.4);
            line-height: 1.1;
            padding-bottom: 10px;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 40%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .hero-subtitle {
            font-size: clamp(1rem, 2.5vw, 1.25rem);
            color: rgba(255, 255, 255, 0.85);
            max-width: 1000px;
            margin: 0 auto 2.5rem auto;
            line-height: 1.7;
        }
        .hero-cta {
            display: inline-block;
            padding: 14px 35px;
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            color: white;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 10px 30px rgba(0, 160, 233, 0.3);
            transition: all 0.3s ease;
        }
        .hero-cta:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 160, 233, 0.4);
        }

        /* EMR Visualization Styles */
        .emr-visualization {
            position: absolute;
            top: 0; left: 0;
            width: 100%; height: 100%;
            z-index: 2;
            pointer-events: none;
        }
        @keyframes pulse-core {
            50% { transform: translate(-50%, -50%) scale(1.05); box-shadow: 0 0 70px rgba(0, 160, 233, 0.5); }
        }
        .data-core {
            position: absolute;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            width: 220px; height: 220px;
            background: radial-gradient(circle, rgba(0, 43, 89, 0.8) 0%, rgba(0, 26, 58, 0.9) 100%);
            border-radius: 50%;
            border: 2px solid rgba(0, 160, 233, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse-core 4s ease-in-out infinite;
        }
        .data-core :global(.icon) { /* Use :global() for nested component styling */
            font-size: 70px;
            color: #00a0e9;
            text-shadow: 0 0 25px rgba(0, 160, 233, 0.7);
        }
        @keyframes orbit-node {
            from { transform: rotate(0deg) translateX(var(--distance)) rotate(0deg); }
            to { transform: rotate(360deg) translateX(var(--distance)) rotate(-360deg); }
        }
        .data-node {
            position: absolute;
            top: 50%; left: 50%;
            margin-top: calc(var(--size) / -2);
            margin-left: calc(var(--size) / -2);
            width: var(--size);
            height: var(--size);
            background: rgba(0, 160, 233, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(0, 160, 233, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: orbit-node 20s linear infinite;
        }
        .data-node :global(.icon) {
            color: #ffffff;
            font-size: calc(var(--size) * 0.4);
            opacity: 0.8;
        }

        /* --- Demo Section Styles --- */
        .new-demo-section {
            padding: 2rem 1rem;
            background: #ffffff;
            text-align: center;
            position: relative;
        }
        .new-demo-section h2 {
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
        }
        .new-demo-section p {
            font-size: 1.2rem;
            color:rgb(247, 244, 248);
            max-width: 900px;
            margin: 0 auto 3rem auto;
            line-height: 1.8;
        }
        .demo-key-points-grid {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 1.5rem;
            margin-top: 2rem;
        }
        .demo-key-point {
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            border-radius: 0.75rem;
            padding: 1.25rem 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex-basis: 220px;
            text-align: center;
        }
        .demo-key-point:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 25px rgba(0, 160, 233, 0.3);
        }
        .demo-key-point :global(.icon) {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        /* --- Video & Features Section --- */
        .video-features-benefits-section {
            padding: 2rem 1rem;
            background: #f8f9fa;
        }
        .video-container {
            background: #000;
            height: 500px;
            width: 100%;
            box-shadow: 0 20px 60px rgba(0, 43, 89, 0.2);
            position: relative;
            overflow: hidden;
            border-radius: 1rem;
        }
        .video-container video {
            width: 100%; height: 100%; object-fit: cover;
        }
        .feature-list-container {
            height: 500px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 1rem;
        }
        .feature-card {
            background: #ffffff;
            border: 1px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0, 41, 86, 0.05);
            transition: all 0.4s ease;
            padding: 1.5rem;
            border-radius: 1rem;
            display: flex;
            align-items: flex-start;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 30px rgba(0, 43, 89, 0.1);
            border-color: rgba(0, 157, 230, 0.5);
        }
        .feature-icon {
            box-shadow: 0 4px 12px rgba(0, 157, 230, 0.2);
            width: 55px; height: 55px;
            min-width: 55px;
            border-radius: 0.5rem;
            margin-right: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
            color: white;
        }
        .feature-icon :global(svg) { font-size: 25px; }
        .feature-card h5 {
            color: #001A3A;
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .feature-card p {
            color: #33475b;
            font-size: 1rem;
            line-height: 1.6;
        }
        
        /* Video Player Styles */
        .video-play-overlay {
            background: linear-gradient(135deg, rgba(0, 160, 233, 0.9) 0%, rgba(0, 86, 179, 0.9) 100%);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            width: 100px; height: 100px;
            transition: all 0.4s ease;
            animation: playButtonPulse 3s ease-in-out infinite;
            cursor: pointer;
        }
        @keyframes playButtonPulse {
            50% { box-shadow: 0 0 0 15px rgba(0, 160, 233, 0.3); }
        }
        .video-controls {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.4s ease-out;
            pointer-events: none;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
        }
        .video-container:hover .video-controls {
            opacity: 1;
            transform: translateY(0);
            pointer-events: all;
        }
        .progress-bar {
            background: linear-gradient(90deg, #002B59 0%, #009DE6 100%) !important;
        }
        
        /* --- Benefits Section --- */
        .benefits-section {
            padding: 2rem 1rem;
            background: linear-gradient(135deg, #001a3a 0%, #002b59 100%);
        }
        .benefits-section h2 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
        }
        .benefit-box {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: #ffffff;
            transition: all 0.4s ease;
            padding: 2rem;
            border-radius: 1rem;
            height: 100%;
        }
        .benefit-box:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(0, 160, 233, 0.5);
        }
        .benefit-box h6 {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ffffff;
        }

        /* Responsive */
        @media (max-width: 991.98px) {
            .hero-section { min-height: 80vh; }
            .emr-visualization { transform: scale(0.7); }
            .video-features-benefits-section .row { flex-direction: column; }
            .feature-list-container, .video-container { height: auto; margin-bottom: 2rem; }
        }
        @media (max-width: 767.98px) {
            .emr-visualization { transform: scale(0.6); top: -10vh; }
            .demo-key-points-grid { grid-template-columns: 1fr 1fr; }
        }
        @media (max-width: 575.98px) {
            .emr-visualization { display: none; } /* Hide complex animation on small screens */
            .demo-key-points-grid { display: block; }
            .demo-key-point { margin-bottom: 1rem; }
        }
        .new-demo-section, .video-features-benefits-section, .benefits-section {
            background: transparent !important;
            color: #f1f5f9 !important;
        }
      `}</style>

            {/* === Page Content Starts Here === */}

            {/* Hero Section */}
            <section ref={heroRef} className="hero-section">
                <div className="emr-visualization" ref={visualizationRef}>
                    <div className="data-core">
                        <FaFileMedical className="icon" />
                    </div>
                    {emrNodes.map((node, index) => (
                        <div
                            key={index}
                            className="data-node"
                            style={{
                                '--size': `${node.size}px`,
                                '--distance': `${node.distance}px`,
                                animationDuration: `${15 + Math.random() * 10}s`,
                                animationDelay: `-${Math.random() * 25}s`,
                                transform: `rotate(${node.angle}deg) translateX(${node.distance}px) rotate(-${node.angle}deg)`,
                            }}
                        >
                            <node.icon className="icon" />
                        </div>
                    ))}
                </div>
                <Container>
                    <div className="hero-content">
                        <h1 ref={titleRef} className="hero-title">
                            The Future of Patient Data Management
                        </h1>
                        <p ref={subtitleRef} className="hero-subtitle">
                            Our advanced Electronic Medical Record (EMR) platform provides a secure, unified, and intelligent view of patient information. Streamline workflows, enhance clinical decisions, and deliver superior care with our intuitive system.
                        </p>
                       
                    </div>
                </Container>
            </section>

            {/* Live EMR Platform Demonstration Section */}
            <section className="new-demo-section" id="demo">
                <Container>
                    <h2 className="hero-title">Live EMR Platform Demonstration</h2>
                    <p >
                        Experience a complete walkthrough of our Electronic Medical Records system. This demo showcases real-world scenarios including integrated charting, secure e-prescribing, and advanced reporting features that drive patient care excellence.
                    </p>
                    <div className="demo-key-points-grid">
                        {demoKeyPoints.map((point, index) => (
                            <div key={index} className="demo-key-point">
                                <point.icon className="icon" />
                                <span>{point.text}</span>
                            </div>
                        ))}
                    </div>
                </Container>
            </section>

            {/* Video and Features Section */}
            <section className="video-features-benefits-section">
                <Container>
                    <Row className="align-items-center">
                        <Col lg={7} className="mb-4 mb-lg-0">
                            <div className="video-container">
                                <video ref={videoRef} onTimeUpdate={handleTimeUpdate} onLoadedMetadata={handleLoadedMetadata} preload="metadata" style={{ borderRadius: '1rem' }}>
                                    <source src={EMRVideo} type="video/mp4" />
                                    Your browser does not support the video tag.
                                </video>
                                {!isPlaying && !isVideoLoading && (
                                    <div className="position-absolute top-50 start-50 translate-middle rounded-circle d-flex align-items-center justify-content-center video-play-overlay" onClick={handlePlayPause}>
                                        <FaPlay size={40} className="text-white" style={{ marginLeft: '5px' }} />
                                    </div>
                                )}
                                <div className="video-controls position-absolute bottom-0 start-0 end-0 p-3 d-flex align-items-center">
                                    <Button variant="transparent" className="text-white" onClick={handlePlayPause}>{isPlaying ? <FaPause /> : <FaPlay />}</Button>
                                    <Button variant="transparent" className="text-white" onClick={handleSkipBackward}><FaFastBackward /></Button>
                                    <Button variant="transparent" className="text-white" onClick={handleSkipForward}><FaFastForward /></Button>
                                    <ProgressBar now={(currentTime / duration) * 100} onClick={handleProgressClick} style={{ cursor: 'pointer', height: '8px', flexGrow: 1 }} />
                                    <span className="text-white small mx-2">{formatTime(currentTime)} / {formatTime(duration)}</span>
                                    <Button variant="transparent" className="text-white" onClick={handleVolumeToggle}>{isMuted ? <FaVolumeOff /> : <FaVolumeUp />}</Button>
                                    <Button variant="transparent" className="text-white" onClick={handleFullscreen}><FaExpand /></Button>
                                </div>
                            </div>
                        </Col>
                        <Col lg={5}>
                            <div className="feature-list-container d-flex flex-column gap-3">
                                {features.map((feature, index) => (
                                    <div key={index} className="feature-card">
                                        <div className="feature-icon">
                                            <feature.icon />
                                        </div>
                                        <div>
                                            <h5>{feature.title}</h5>
                                            <p className="mb-0">{feature.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </Col>
                    </Row>
                </Container>
            </section>

            {/* Benefits Section */}
            <section className="benefits-section">
                <Container>
                    <h2 className="text-center mb-5">Why Choose Our EMR?</h2>
                    <Row>
                        {benefits.map((benefit, index) => (
                            <Col md={6} lg={3} key={index} className="mb-4">
                                <div className="benefit-box d-flex align-items-center justify-content-center text-center">
                                    <h6>{benefit}</h6>
                                </div>
                            </Col>
                        ))}
                    </Row>
                </Container>
            </section>

        </div>
    );
};

export default EMRDemo;