import React, { useRef, useState, useEffect } from 'react';

import {
  FaPlay, FaPause, FaVolumeUp, FaVolumeOff, FaExpand, FaUsers,
  FaChartLine, FaClock, FaRocket, FaEnvelope,
  FaFastBackward, FaFastForward, FaFileMedical, FaChartBar, FaShieldAlt, FaRobot,
  FaMobileAlt, FaMagic, FaStethoscope, FaChartPie, FaLock // FaCheckCircle REMOVED from import
} from 'react-icons/fa';
import MTIVideo from '../Asserts/MTI.mp4';


const TradingIntelligence = () => {
  // Refs for video player and hero section elements
  const videoRef = useRef(null);
  const heroRef = useRef(null);
  const titleRef = useRef(null);
  const subtitleRef = useRef(null);
  const ctaRef = useRef(null);
  const visualizationRef = useRef(null);

  // State for video player
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [videoError, setVideoError] = useState(null);

  // Original Features data based on Screenshot (45) - KEPT
  const features = [
    {
      icon: FaFileMedical,
      title: "AI-Powered Analytics",
      description: "Advanced machine learning predicts trading opportunities."
    },
    {
      icon: FaChartBar,
      title: "Real-Time Market Intelligence",
      description: "Get instant market insights with real-time data processing."
    },
    {
      icon: FaShieldAlt,
      title: "Automated Trading Strategies",
      description: "Deploy automated trading strategies that adapt and optimize."
    },
    {
      icon: FaMobileAlt,
      title: "Risk Management",
      description: "Risk management tools to protect investments and maximize returns."
    }
  ];


  // Key points data for the NEW "Live MTI Platform Demonstration" section
  const demoKeyPoints = [
    { icon: FaUsers, text: "AI-Powered Analytics" },
    { icon: FaChartLine, text: "Real-time Market Data" },
    { icon: FaRobot, text: "Automated Trading" },
    { icon: FaMobileAlt, text: "Risk Management" }
  ];

  // Benefits data based on Screenshot (45)
  const benefits = [
    "Advanced algorithmic trading capabilities",
    "Real-time market data and analytics",
    "Customizable trading dashboards",
    "Risk management and compliance tools",
    "Multi-asset class support",
    "API integrations with major exchanges",
    "Real-time market analytics and insights",
    "Advanced risk management tools"
  ];

  // Video player handlers (KEPT AS IS)
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setIsVideoLoading(false); // Video metadata loaded
    }
  };

  const handleProgressClick = (e) => {
    if (videoRef.current) {
      const progressBar = e.currentTarget;
      const clickX = e.nativeEvent.offsetX;
      const newTime = (clickX / progressBar.offsetWidth) * duration;
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleVolumeToggle = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoRef.current.muted;
      setIsMuted(videoRef.current.muted);
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (videoRef.current.requestFullscreen) {
        videoRef.current.requestFullscreen();
      } else if (videoRef.current.mozRequestFullScreen) { // Firefox
        videoRef.current.mozRequestFullScreen();
      } else if (videoRef.current.webkitRequestFullscreen) { // Chrome, Safari and Opera
        videoRef.current.webkitRequestFullscreen();
      } else if (videoRef.current.msRequestFullscreen) { // IE/Edge
        videoRef.current.msRequestFullscreen();
      }
    }
  };

  const handleSkipForward = () => {
    if (videoRef.current) {
      videoRef.current.currentTime += 15; // Skip forward 15 seconds
    }
  };

  const handleSkipBackward = () => {
    if (videoRef.current) {
      videoRef.current.currentTime -= 15; // Skip backward 15 seconds
    }
  };

  const handleCanPlay = () => {
    setIsVideoLoading(false); // Video can start playing
    setVideoError(null);
  };

  const handleVideoError = () => {
    setVideoError('Error loading video. Please try again later.');
    setIsVideoLoading(false);
  };

  // Hero section scroll animation effect using Intersection Observer (UNCHANGED)
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            if (titleRef.current) titleRef.current.style.animation = 'fadeInUp 1s ease-out both';
            if (subtitleRef.current) subtitleRef.current.style.animation = 'fadeInUp 1s ease-out 0.2s both';
            if (ctaRef.current) ctaRef.current.style.animation = 'fadeInUp 1s ease-out 0.4s both';
            if (visualizationRef.current) visualizationRef.current.style.animation = 'fadeInUp 1s ease-out 0.6s both';
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (heroRef.current) {
      observer.observe(heroRef.current);
    }

    return () => {
      if (heroRef.current) {
        observer.unobserve(heroRef.current);
      }
    };
  }, []);

  // Intersection Observer for ALL sections that animate (Video/Features, New Demo, Benefits)
  useEffect(() => {
    const videoFeaturesElements = document.querySelectorAll('.video-features-benefits-section .col-lg-7, .video-features-benefits-section .col-lg-5 .feature-card'); // Updated col-lg classes
    const demoSectionElements = document.querySelectorAll('.new-demo-section h2, .new-demo-section p, .demo-key-point');
    const benefitBoxes = document.querySelectorAll('.benefits-section .benefit-box');

    const allElementsToAnimate = [...videoFeaturesElements, ...demoSectionElements, ...benefitBoxes];

    const sectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.animation = 'fadeInUp 0.8s ease-out both';

            // Stagger delays based on element type and index within its group
            // Updated for col-lg-7 (standard video container)
            if (entry.target.classList.contains('col-lg-7')) { // Video container
              entry.target.style.animationDelay = '0s';
            } else if (entry.target.classList.contains('feature-card')) { // Feature cards
              const index = Array.from(entry.target.parentNode.children).indexOf(entry.target);
              entry.target.style.animationDelay = `${0.2 + index * 0.1}s`;
            } else if (entry.target.classList.contains('demo-key-point')) { // New demo points
              const index = Array.from(entry.target.parentNode.children).indexOf(entry.target);
              entry.target.style.animationDelay = `${0.3 + index * 0.1}s`;
            } else if (entry.target.classList.contains('benefit-box')) { // Benefit boxes
              const index = Array.from(entry.target.parentNode.children).indexOf(entry.target);
              entry.target.style.animationDelay = `${0.1 + index * 0.1}s`;
            }
            // For H2/P in new demo section, default delay from general fadeInUp is fine or can set 0s
            if ((entry.target.tagName === 'H2' || entry.target.tagName === 'P') && entry.target.closest('.new-demo-section')) {
              entry.target.style.animationDelay = '0s';
            }

            sectionObserver.unobserve(entry.target); // Stop observing after animation
          }
        });
      },
      { threshold: 0.15 } // Trigger when 15% of the element is visible
    );

    // Apply initial hidden state and observe all elements
    allElementsToAnimate.forEach((section) => {
      section.style.opacity = '0';
      section.style.transform = 'translateY(30px)';
      sectionObserver.observe(section);
    });

    return () => {
      allElementsToAnimate.forEach((section) => {
        sectionObserver.unobserve(section);
      });
    };
  }, []);

  return (
    <div className="trading-intelligence-page" style={{ background: '#012855', color: '#f1f5f9', minHeight: '100vh' }}>
      <style jsx>{`
                /* Basic Page Wrapper and Global Resets */
                .trading-intelligence-page {
                    overflow-x: hidden; /* Prevents horizontal scroll */
                    font-family: 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                    -webkit-font-smoothing: antialiased;
                    -moz-osx-font-smoothing: grayscale;
                    color: #f1f5f9;
                    background: #001A3A;
                    min-height: 100vh;
                }

                /* --- General Animations (used across multiple sections) --- */
                @keyframes fadeInUp {
                    from {
                        opacity: 0;
                        transform: translateY(30px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                /* Hero Section Specific Styles - Enhanced */
                .hero-section {
                    position: relative;
                    min-height: 100vh;
                    background: #012855;
                    display: flex;
                    align-items: center;
                    overflow: hidden;
                    padding: 80px 0 60px 0;
                    margin-top: 0;
                }

                .hero-section::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background-image: linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px);
                    background-size: 50px 50px;
                    opacity: 0.2;
                    pointer-events: none;
                    z-index: 1;
                }

                /* Animated Background Elements - Floating Geometric Shapes */
                @keyframes float {
                    0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.7; }
                    33% { transform: translateY(-25px) translateX(15px) rotate(100deg); opacity: 1; }
                    66% { transform: translateY(10px) translateX(-20px) rotate(200deg); opacity: 0.8; }
                }

                /* Animated Background Elements - Floating Particles */
                @keyframes particleFloat {
                    0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.6; }
                    33% { transform: translateY(-20px) translateX(10px) rotate(120deg); opacity: 1; }
                    66% { transform: translateY(10px) translateX(-15px) rotate(240deg); opacity: 0.8; }
                }

                /* Enhanced Typography and Text Styling */
                .hero-section h1, .hero-section .display-1 {
                    color: #ffffff;
                    font-weight: 800;
                    letter-spacing: 2px;
                    text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
                    margin-bottom: 2rem;
                }

                .hero-section p, .hero-section .lead {
                    color: rgba(255, 255, 255, 0.9);
                    font-weight: 400;
                    line-height: 1.8;
                    margin-bottom: 2rem;
                }

                /* Non-hero section text styling */
                .new-demo-section h2,
                .video-features-benefits-section h2,
                .feature-card h5 {
                    color: #001A3A;
                    font-weight: 700;
                    letter-spacing: 1px;
                }

                .new-demo-section p,
                .video-features-benefits-section p,
                .feature-card p,
                .benefit-box h6 {
                    color: #001A3A;
                    line-height: 1.7;
                }

                /* Stats section styling */
                .hero-stats h3 {
                    color: #00a0e9;
                    font-weight: 800;
                }

                .hero-stats p {
                    color: rgba(255, 255, 255, 0.8);
                }

                /* Animated Background Elements - Gradient Orbs */
                @keyframes floatOrb1 {
                    0%, 100% { transform: translate(0, 0); }
                    50% { transform: translate(-30px, 50px); }
                }
                @keyframes floatOrb2 {
                    0%, 100% { transform: translate(0, 0); }
                    50% { transform: translate(40px, -60px); }
                }

                /* Hero Content Text and Styling */
                .display-1 {
                    font-size: clamp(3rem, 8vw, 4.4rem);
                    line-height: 1.1;
                    background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
                    letter-spacing: -1px;
                }
                .lead {
                    font-size: 1.2rem;
                    line-height: 1.6;
                    color: rgba(255, 255, 255, 0.9);
                    max-width: 600px;
                    font-weight: 300;
                }

                /* Hero Stats Section */
                .hero-stats .col-4 {
                    /* Initial animation handled by JS observer + fadeInUp */
                }
                .hero-stats .p-3 {
                    background: rgba(255, 255, 255, 0.05);
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    border-radius: 0.75rem; /* Consistent border-radius */
                }
                .hero-stats h3 {
                    color: #00a0e9;
                    font-size: 1.8rem;
                    font-weight: 700;
                }
                .hero-stats p {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 0.95rem;
                }

                /* Hero Visualization */
                @keyframes pulse {
                    0% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; box-shadow: 0 0 40px rgba(0, 160, 233, 0.4); }
                    50% { transform: translate(-50%, -50%) scale(1.05); opacity: 1; box-shadow: 0 0 70px rgba(0, 160, 233, 0.6); }
                    100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; box-shadow: 0 0 40px rgba(0, 160, 233, 0.4); }
                }
                @keyframes orbit {
                    from { transform: translate(-50%, -50%) rotate(0deg) translateX(var(--radius)) rotate(0deg); }
                    to { transform: translate(-50%, -50%) rotate(360deg) translateX(var(--radius)) rotate(-360deg); }
                }
                @keyframes dash {
                    to { stroke-dashoffset: -20; }
                }


                /* --- NEW: Live MTI Platform Demo Section Styles --- */
                .new-demo-section {
                  padding: 2rem 0;
                  background: #012855;
                  text-align: center;
                  position: relative;
                  overflow: hidden;
                }

                /* Removed the grid overlay for the new demo section as it's now white */
                .new-demo-section::before {
                    content: none; /* Explicitly remove the before pseudo-element */
                }

                /* Initial animation states for elements in this new section */
                .new-demo-section h2,
                .new-demo-section p,
                .demo-key-point {
                    opacity: 0; /* Hidden initially */
                    transform: translateY(30px); /* Off-set for slide-up effect */
                    transition: opacity 0.8s ease-out, transform 0.8s ease-out; /* Smooth transition for JS animation */
                }

                .new-demo-section h2 {
                    font-size: clamp(2rem, 5vw, 3rem);
                    font-weight: 700;
                    margin-bottom: 1.5rem;
                    position: relative;
                    z-index: 2;
                    background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
                }

                .new-demo-section p {
                    font-size: 1.1rem;
                    color: #f1f5f9;
                    max-width: 800px;
                    margin: 0 auto 3rem auto;
                    line-height: 1.7;
                    position: relative;
                    z-index: 2;
                }

                .demo-key-points-grid {
                    display: flex;
                    justify-content: center;
                    flex-wrap: wrap;
                    gap: 1.5rem; /* Space between points */
                    margin: 2rem 0 1rem 0; /* Reduced bottom margin to fix spacing */
                    position: relative;
                    z-index: 2;
                }

               .demo-key-point {
   background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(0, 160, 233, 0.3);
    border-radius: 0.75rem;
    padding: 1.25rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-basis: calc(25% - 1.125rem);
    min-width: 220px;
    justify-content: center;
    text-align: center; /* To center text below the icon */
}

                .demo-key-point:hover {
                    transform: translateY(-8px);
                    box-shadow: 0 0 50px rgba(0, 160, 233, 0.5), 0 0 20px rgba(255, 255, 255, 0.2);
                    border-color: rgba(0, 160, 233, 0.8);
                }

                .demo-key-point .icon {
                    font-size: 1.5rem;
                    color:White;
                    
                }
                /* END OF NEW DEMO SECTION STYLES */


                /* --- Video & Features Section Styles (Screenshot 45) - ADJUSTED FOR VIDEO SIZE --- */
                .video-features-benefits-section {
                  padding: 2rem 0;
                  background: #012855;
                }

                /* Initial animation states for video/features/benefits sections */
                /* Updated col-lg classes for animation */
                .video-features-benefits-section .col-lg-7,
                .video-features-benefits-section .col-lg-5 .feature-card {
                  opacity: 0; /* Hidden initially */
                  transform: translateY(30px); /* Off-set for slide-up effect */
                  transition: opacity 0.8s ease-out, transform 0.8s ease-out; /* Smooth transition for JS animation */
                }

                .video-container {
                    background: linear-gradient(135deg, #000 0%, #1a1a1a 100%);
                    aspect-ratio: 16/9;
                    height: 500px; /* Fixed height for consistent matching */
                    max-height: 80vh; /* Responsive height constraint */
                    width: 100%;
                    box-shadow: 0 20px 60px rgba(0, 43, 89, 0.3), 0 8px 25px rgba(0, 157, 230, 0.2);
                    border: 2px solid rgba(0, 157, 230, 0.1);
                    position: relative;
                    overflow: hidden;
                    border-radius: 1rem; /* Matched from screenshot */
                }

                /* Feature cards container to match video height */
                .feature-list-container {
                    height: 500px; /* Match video container height */
                    max-height: 80vh; /* Match video responsive constraint */
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    gap: 1rem;
                }

                /* Responsive video adjustments */
                @media (max-width: 768px) {
                    .video-container {
                        max-height: 70vh;
                    }
                }
                @media (max-width: 576px) {
                    .video-container {
                        max-height: 60vh;
                    }
                }

                .video-container::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(45deg, transparent 30%, rgba(0, 157, 230, 0.05) 50%, transparent 70%);
                    pointer-events: none;
                    z-index: 1;
                }

                /* Video overlay styles */
                .video-overlay {
                    position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                    display: flex; align-items: center; justify-content: center;
                    background-color: rgba(0,0,0,0.5); z-index: 5;
                    transition: opacity 0.3s ease;
                }
                .video-container:hover .video-overlay-hidden {
                    opacity: 1 !important;
                }
                @keyframes playButtonPulse {
                    50% { box-shadow: 0 0 0 15px rgba(0, 160, 233, 0.3); transform: scale(1.1); }
                }
                .play-button {
                    background: linear-gradient(135deg, rgba(0, 160, 233, 0.9) 0%, rgba(0, 86, 179, 0.9) 100%);
                    backdrop-filter: blur(10px);
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    width: 90px; height: 90px;
                    border-radius: 50%; cursor: pointer;
                    display: flex; align-items: center; justify-content: center;
                    animation: playButtonPulse 3s ease-in-out infinite;
                    transition: transform 0.3s;
                }
                .play-button:hover { transform: scale(1.1); }

                /* Video controls */
                .video-controls {
                    position: absolute; bottom: 0; left: 0; right: 0;
                    opacity: 0; transform: translateY(100%);
                    transition: all 0.4s ease-out;
                    pointer-events: none;
                    background: linear-gradient(transparent, rgba(0,0,0,0.8));
                    padding: 0.75rem 1rem; z-index: 10;
                }
                .video-container:hover .video-controls {
                    opacity: 1;
                    transform: translateY(0);
                    pointer-events: all;
                }
                .video-controls .btn {
                    background: transparent; border: none;
                    color: white; font-size: 1.1rem; padding: 0.5rem;
                }
                .video-controls .btn:hover { color: #00a0e9; }
                .progress-bar-container {
                    flex-grow: 1; height: 8px;
                    background-color: rgba(255,255,255,0.3);
                    border-radius: 4px; cursor: pointer; margin: 0 1rem;
                }
                .progress-bar {
                    height: 100%; background-color: #00a0e9;
                    border-radius: 4px;
                }
                .time-display {
                    font-size: 0.9rem; color: white;
                    min-width: 90px; text-align: center;
                }

                /* Progress bar */
                .progress {
                    height: 8px !important;
                    cursor: pointer;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 3px;
                    transition: all 0.3s ease-in-out !important;
                    position: relative; /* For the scrubber thumb */
                }
                .progress:hover {
                    transform: scaleY(1.5);
                }
                .progress-bar {
                    background: linear-gradient(90deg, #002B59 0%, #009DE6 100%) !important;
                    border-radius: 3px;
                    transition: width 0.1s ease;
                    position: relative; /* For the scrubber thumb */
                }
                .progress-bar::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 100%;
                    transform: translate(-50%, -50%);
                    width: 12px;
                    height: 12px;
                    background: #fff;
                    box-shadow: 0 0 10px rgba(0, 157, 230, 0.7);
                    border-radius: 50%;
                    opacity: 1;
                    pointer-events: none;
                    z-index: 1;
                }

                /* Enhanced Play button overlay */
                .video-play-overlay {
                    background: linear-gradient(135deg, rgba(0, 160, 233, 0.95) 0%, rgba(0, 86, 179, 0.95) 100%);
                    backdrop-filter: blur(20px);
                    border: 3px solid rgba(255, 255, 255, 0.3);
                    box-shadow:
                        0 0 0 0 rgba(0, 160, 233, 0.7),
                        0 25px 50px rgba(0, 43, 89, 0.4),
                        0 10px 25px rgba(0, 0, 0, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
                    width: 100px;
                    height: 100px;
                    transition: all 0.4s cubic-bezier(0.2, 0.8, 0.2, 1);
                    animation: playButtonPulse 3s ease-in-out infinite;
                    position: relative;
                    overflow: hidden;
                    cursor: pointer;
                }
                .video-play-overlay::before {
                    content: '';
                    position: absolute;
                    top: -50%;
                    left: -50%;
                    width: 200%;
                    height: 200%;
                    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
                    transform: rotate(-45deg);
                    transition: transform 0.6s ease;
                }
                .video-play-overlay:hover::before {
                    transform: rotate(-45deg) translateX(100%);
                }
                .video-play-overlay:hover {
                    transform: scale(1.15);
                    box-shadow:
                        0 0 0 20px rgba(0, 160, 233, 0.2),
                        0 30px 60px rgba(0, 43, 89, 0.5),
                        0 15px 35px rgba(0, 0, 0, 0.4),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3);
                    border-color: rgba(255, 255, 255, 0.5);
                }
                .video-play-overlay:active {
                    transform: scale(1.05);
                }
                @keyframes playButtonPulse {
                    0%, 100% { box-shadow: 0 0 0 0 rgba(0, 160, 233, 0.7), 0 25px 50px rgba(0, 43, 89, 0.4), 0 10px 25px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2); }
                    50% { box-shadow: 0 0 0 15px rgba(0, 160, 233, 0.3), 0 25px 50px rgba(0, 43, 89, 0.4), 0 10px 25px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2); }
                }
                .play-button-ring {
                    position: absolute;
                    border: 2px solid rgba(0, 160, 233, 0.4);
                    border-radius: 50%;
                    animation: playButtonRings 2s ease-out infinite;
                }
                .play-button-ring:nth-child(1) { width: 120px; height: 120px; animation-delay: 0s; }
                .play-button-ring:nth-child(2) { width: 150px; height: 150px; animation-delay: 0.5s; }
                .play-button-ring:nth-child(3) { width: 180px; height: 180px; animation-delay: 1s; }
                @keyframes playButtonRings {
                    0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
                    100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
                }

                /* --- Features List Styles (within video section) --- */
                .feature-card {
                    background: white;
                    border: 1px solid rgba(0, 157, 230, 0.1);
                    box-shadow: 0 4px 15px rgba(0, 43, 89, 0.06);
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    padding: 12px;
                    border-radius: 0.75rem;
                    display: flex;
                    align-items: flex-start; /* Align icon to top if text wraps */
                    margin-bottom: 1rem;
                }
                .feature-list-container > div:last-child .feature-card {
                    margin-bottom: 0;
                }
                .feature-card:hover {
                    transform: translateY(-8px);
                    box-shadow: 0 15px 40px rgba(0, 43, 89, 0.15), 0 5px 15px rgba(0, 157, 230, 0.1);
                    border-color: rgba(0, 157, 230, 0.5);
                }
                .feature-icon {
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 12px rgba(0, 157, 230, 0.25);
                    width: 55px;
                    height: 55px;
                    min-width: 55px;
                    min-height: 55px;
                    border-radius: 0.5rem;
                    margin-right: 1.25rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: linear-gradient(135deg, #00a0e9 0%, #0056b3 100%); /* Blue gradient */
                    color: white;
                }
                .feature-card:hover .feature-icon {
                    transform: scale(1.1);
                    box-shadow: 0 6px 18px rgba(0, 157, 230, 0.4);
                }
                .feature-card h5 {
                    color: #002B59;
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin-bottom: 0.5rem;
                }
                .feature-card p {
                    color: #6c757d;
                    font-size: 1rem;
                    line-height: 1.6;
                }

                /* --- Benefits Section Styles --- */
                .benefits-section {
                    padding-top: 2rem;
                    padding-bottom: 2rem;
                    background: #012855;
                }
                .benefits-section h2 {
                    font-size: clamp(2.2rem, 5vw, 3rem);
                    font-weight: 800;
                    text-align: center;
                    margin-bottom: 3rem;
                    background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                    text-shadow: 0 0 30px rgba(0, 160, 233, 0.3);
                }

                /* Initial animation states for elements in benefits section */
                .benefits-section .benefit-box {
                    opacity: 0;
                    transform: translateY(30px);
                    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
                }

                .benefit-box {
                    background: white;
                    color: #002B59;
                    transition: all 0.3s ease;
                    border: 1px solid rgba(0, 157, 230, 0.1);
                    box-shadow: 0 4px 15px rgba(0, 43, 89, 0.06);
                    padding: 1.5rem;
                    border-radius: 0.75rem;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    text-align: center;
                }
                .benefit-box:hover {
                    transform: translateY(-8px);
                    box-shadow: 0 15px 40px rgba(0, 43, 89, 0.15), 0 5px 15px rgba(0, 157, 230, 0.1);
                    border-color: rgba(0, 157, 230, 0.5);
                }
                /* Removed .benefit-box .text-success styling as FaCheckCircle is removed */
                .benefit-box h6 {
                    font-size: 1.1rem;
                    font-weight: 600;
                    color: #002B59;
                }

                /* General Button Hover Effect */
                .btn:hover {
                    transform: translateY(-2px);
                    transition: transform 0.2s ease;
                }

                /* Responsive Adjustments */
                @media (max-width: 991.98px) { /* Tablet and smaller */
                    .hero-section {
                        min-height: 80vh;
                        padding: 100px 0 60px 0; /* Increased top padding to prevent header overlap */
                        margin-top: 0;
                    }
                    .hero-section .row {
                        flex-direction: column;
                        text-align: center;
                    }
                    .hero-section .col-lg-6:nth-child(2) {
                        display: none;
                    }
                    .hero-stats {
                        margin-top: 3rem !important;
                        padding: 0 1rem;
                        display: grid;
                        grid-template-columns: repeat(3, 1fr); /* Keep horizontal layout */
                        gap: 1rem;
                    }
                    .hero-section h1, .hero-section .display-1 {
                        font-size: clamp(2.5rem, 8vw, 3.5rem);
                        margin-bottom: 1.5rem;
                        z-index: 10;
                        position: relative;
                    }
                    .hero-section p, .hero-section .lead {
                        font-size: clamp(1rem, 4vw, 1.2rem);
                        margin-bottom: 2rem;
                        padding: 0 1rem;
                    }
                    /* Fix video and feature cards height matching on tablet */
                    .video-container {
                        height: 400px;
                        max-height: 70vh;
                    }
                    .feature-list-container {
                        height: 400px;
                        max-height: 70vh;
                    }

                    .video-features-benefits-section .row {
                        flex-direction: column;
                    }
                    /* Updated col-lg classes for responsiveness */
                    .video-features-benefits-section .col-lg-7,
                    .video-features-benefits-section .col-lg-5 {
                        width: 100%;
                        max-width: 100%;
                        margin-bottom: 2rem;
                    }
                    .video-features-benefits-section .col-lg-5 { /* Features column */
                        margin-top: 2rem;
                    }

                    .demo-key-points-grid {
                        display: grid;
                        grid-template-columns: repeat(2, 1fr); /* 2x2 grid layout */
                        gap: 1rem;
                        justify-items: center;
                        margin: 2rem 0 1rem 0;
                    }
                    .demo-key-point {
                        flex-basis: 100%;
                        max-width: 280px;
                        width: 100%;
                    }

                    .benefits-section .col-md-6 {
                        margin-bottom: 1.5rem;
                    }
                    .benefits-section .col-md-6:last-child {
                        margin-bottom: 0;
                    }
                }

                @media (max-width: 767.98px) { /* Mobile devices */
                    .hero-section {
                        min-height: 70vh;
                        padding: 120px 0 40px 0; /* Increased top padding to prevent header overlap */
                        margin-top: 0;
                    }
                    .hero-section h1, .hero-section .display-1 {
                        font-size: clamp(2rem, 10vw, 2.8rem);
                        margin-bottom: 1rem;
                        z-index: 10;
                        position: relative;
                    }
                    .hero-section p, .hero-section .lead {
                        font-size: clamp(0.9rem, 5vw, 1.1rem);
                        padding: 0 1.5rem;
                    }
                    .hero-stats {
                        margin-top: 2rem !important;
                        padding: 0 1.5rem;
                        display: grid;
                        grid-template-columns: repeat(3, 1fr); /* Keep horizontal layout */
                        gap: 0.5rem;
                    }
                    .hero-stats .col-4 {
                        margin-bottom: 0; /* Remove bottom margin for grid layout */
                    }
                    /* Mobile video and feature cards */
                    .video-container {
                        height: 300px;
                        max-height: 60vh;
                    }
                    .feature-list-container {
                        height: auto; /* Allow natural height on mobile */
                        margin-top: 2rem;
                    }
                @media (max-width: 767.98px) { /* For Bootstrap's md breakpoint and smaller */
                    .video-features-benefits-section,
                    .new-demo-section,
                    .benefits-section {
                        padding-top: 3rem;
                        padding-bottom: 3rem;
                    }
                    .feature-card {
                        flex-direction: column;
                        text-align: center;
                        padding: 1.25rem;
                    }
                    .feature-icon {
                        margin-right: 0;
                        margin-bottom: 1rem;
                    }
                    .feature-card h5 {
                        font-size: 1.15rem;
                    }
                    .feature-card p {
                        font-size: 0.9rem;
                    }
                    .new-demo-section h2 {
                        font-size: clamp(1.8rem, 7vw, 2.5rem);
                    }
                    .new-demo-section p {
                        font-size: 1rem;
                        margin-bottom: 2rem;
                    }
                    .demo-key-point {
                        padding: 1rem 1.5rem;
                        font-size: 1rem;
                    }
                    .demo-key-point .icon {
                        font-size: 1.3rem;
                    }
                    .benefit-box {
                        padding: 1.5rem;
                    }
                    .benefit-box h6 {
                        font-size: 1rem;
                    }
                    .hero-stats .col-4 {
                        margin-bottom: 1rem;
                    }
                }

                @media (max-width: 575.98px) { /* Small mobile devices */
                    .hero-section {
                        padding: 140px 0 30px 0; /* Increased top padding for small mobile */
                    }
                    .hero-section h1, .hero-section .display-1 {
                        font-size: clamp(1.8rem, 12vw, 2.5rem);
                        letter-spacing: 1px;
                        z-index: 10;
                        position: relative;
                    }
                    .hero-section p, .hero-section .lead {
                        font-size: clamp(0.85rem, 6vw, 1rem);
                        padding: 0 2rem;
                    }
                    .hero-stats {
                        padding: 0 2rem;
                        grid-template-columns: 1fr; /* Stack stats vertically on very small screens */
                        gap: 1rem;
                        text-align: center;
                    }
                    /* Small mobile demo cards - single column */
                    .demo-key-points-grid {
                        grid-template-columns: 1fr; /* Single column on very small screens */
                        gap: 1rem;
                        margin: 1.5rem 0 0.5rem 0;
                    }
                    .demo-key-point {
                        max-width: 100%;
                        padding: 1rem;
                    }
                    .video-controls .btn {
                        width: 38px;
                        height: 38px;
                        font-size: 1rem;
                    }
                    .time-display { display: none; }
                }
                .new-demo-section, .video-features-benefits-section, .benefits-section {
                    background: transparent !important;
                    color: #f1f5f9 !important;
                }
            `}</style>

      {/* Extraordinary Hero Section (Screenshot 43) - NO CHANGES APPLIED HERE */}
      <section
        ref={heroRef}
        className="hero-section position-relative"
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1, pointerEvents: 'none' }}>
          {/* Floating Geometric Shapes */}
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="position-absolute"
              style={{
                width: '50px',
                height: '50px',
                background: 'rgba(0, 160, 233, 0.2)',
                borderRadius: '10px',
                top: `${Math.random() * 90}%`,
                left: `${Math.random() * 90}%`,
                filter: 'blur(5px)',
                animation: `float 6s ease-in-out infinite alternate`,
                animationDelay: `${Math.random() * 2}s`
              }}
            />
          ))}

          {/* Floating Particles */}
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="position-absolute"
              style={{
                width: '2px',
                height: '2px',
                background: 'white',
                borderRadius: '50%',
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: 0.5,
                animation: `particleFloat 8s linear infinite`,
                animationDelay: `${Math.random() * 4}s`
              }}
            />
          ))}

          {/* Gradient Orbs */}
          <div
            className="position-absolute"
            style={{
              width: '300px',
              height: '300px',
              background: 'radial-gradient(circle, rgba(0, 160, 233, 0.1) 0%, transparent 70%)',
              borderRadius: '50%',
              top: '10%',
              right: '5%',
              filter: 'blur(30px)',
              animation: 'floatOrb1 7s ease-in-out infinite'
            }}
          />
          <div
            className="position-absolute"
            style={{
              width: '200px',
              height: '200px',
              background: 'radial-gradient(circle, rgba(0, 160, 233, 0.08) 0%, transparent 70%)',
              borderRadius: '50%',
              bottom: '10%',
              left: '5%',
              filter: 'blur(25px)',
              animation: 'floatOrb2 9s ease-in-out infinite'
            }}
          />
        </div>

        <div className="container position-relative" style={{ zIndex: 2 }}>
          <div className="row align-items-center min-vh-100">
            <div className="col-lg-6 text-white">
              <div className="mb-4">
                <h1
                  ref={titleRef}
                  className="display-1 fw-bold mb-4"
                  style={{
                    animation: 'none', /* Will be set by JS on intersect */
                    
                  }}
                >
                  Makonis.<br />
                  <span style={{ color: '#00a0e9' , paddingBottom: '10px'}}>Trading Intelligence.</span>
                </h1>
              </div>

              <div>
                <p
                  ref={subtitleRef}
                  className="lead mb-5"
                  style={{
                    animation: 'none' /* Will be set by JS on intersect */
                  }}
                >
                  Revolutionize your trading operations with our cutting-edge AI-powered
                  trading intelligence platform. Harness advanced analytics, real-time market
                  insights, and automated strategies to maximize your trading performance and
                  stay ahead of market movements.
                </p>
              </div>

              {/* Stats Section */}
              <div
                ref={ctaRef}
                className="row mt-5 pt-4 hero-stats"
                style={{
                  animation: 'none' /* Will be set by JS on intersect */
                }}
              >
                {[
                ].map((stat, index) => (
                  <div key={index} className="col-4 text-center">
                    <div className="p-3 rounded-4">
                      <h3 className="fw-bold mb-1">{stat.number}</h3>
                      <p className="small mb-0">{stat.label}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="col-lg-6 d-none d-lg-block">
              <div
                ref={visualizationRef}
                className="position-relative"
                style={{
                  height: '600px',
                  perspective: '1000px',
                  animation: 'none' /* Will be set by JS on intersect */
                }}
              >
                <div
                  className="position-absolute top-50 start-50 translate-middle"
                  style={{
                    width: '200px',
                    height: '200px',
                    background: 'linear-gradient(135deg, rgba(0, 160, 233, 0.2), rgba(0, 160, 233, 0.4))',
                    borderRadius: '50%',
                    backdropFilter: 'blur(20px)',
                    border: '2px solid rgba(0, 160, 233, 0.3)',
                    boxShadow: '0 0 60px rgba(0, 160, 233, 0.4), inset 0 0 10px rgba(255,255,255,0.1)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    animation: 'pulse 3s ease-in-out infinite'
                  }}
                >
                  <FaUsers
                    style={{
                      fontSize: '80px',
                      color: '#00a0e9',
                      filter: 'drop-shadow(0 0 20px rgba(0, 160, 233, 0.8))'
                    }}
                  />
                </div>

                {[
                  { icon: FaChartLine, angle: 0, radius: 150, color: '#00a0e9' },
                  { icon: FaClock, angle: 60, radius: 180, color: '#0056b3' },
                  { icon: FaShieldAlt, angle: 120, radius: 160, color: '#00a0e9' },
                  { icon: FaRocket, angle: 180, radius: 170, color: '#0056b3' },
                  { icon: FaEnvelope, angle: 240, radius: 155, color: '#00a0e9' }, // FaCheckCircle changed to FaEnvelope here to avoid error in the Hero visualization
                  { icon: FaEnvelope, angle: 300, radius: 175, color: '#0056b3' }
                ].map((item, index) => (
                  <div
                    key={index}
                    className="position-absolute"
                    style={{
                      top: '50%',
                      left: '50%',
                      transform: `translate(-50%, -50%) rotate(${item.angle}deg) translateX(${item.radius}px) rotate(-${item.angle}deg)`,
                      width: '80px',
                      height: '80px',
                      background: `linear-gradient(135deg, ${item.color}20, ${item.color}40)`,
                      borderRadius: '20px',
                      backdropFilter: 'blur(10px)',
                      border: `1px solid ${item.color}30`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: `0 8px 32px ${item.color}20`,
                      animation: `orbit 20s linear infinite`,
                      animationDelay: `${index * -3.33}s`,
                      '--radius': `${item.radius}px`
                    }}
                  >
                    <item.icon
                      style={{
                        fontSize: '24px',
                        color: item.color
                      }}
                    />
                  </div>
                ))}

                {/* Connection Lines */}
                <svg
                  className="position-absolute top-0 start-0 w-100 h-100"
                  style={{ zIndex: -1 }}
                >
                  {[...Array(6)].map((_, i) => (
                    <line
                      key={i}
                      x1="50%"
                      y1="50%"
                      x2={`${50 + 25 * Math.cos(i * Math.PI / 3)}%`}
                      y2={`${50 + 25 * Math.sin(i * Math.PI / 3)}%`}
                      stroke="rgba(0, 160, 233, 0.3)"
                      strokeWidth="2"
                      strokeDasharray="5,5"
                      style={{
                        animation: `dash 2s linear infinite`,
                        animationDelay: `${i * 0.3}s`
                      }}
                    />
                  ))}
                </svg>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* NEW: Live ATS Platform Demonstration Section (Now moved up and with white background) */}
      <section className="new-demo-section">
        {/* Removed the grid background overlay */}
        <div className="container" style={{ position: 'relative', zIndex: 2 }}> {/* Content above background */}
          <div className="new-demo-section-content p-2">
            <h2>  Makonis Trading Intelligence Platform Demo</h2>

            <p>
                  Discover the power of our advanced trading intelligence platform. This comprehensive demo
                  showcases real-time market analysis, AI-powered trading strategies, risk management tools,
                  and automated execution capabilities that drive superior trading performance.
            </p>

            <div className="w-30 h-1 mx-auto relative">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>
            <div className="demo-key-points-grid">
              {demoKeyPoints.map((point, index) => (
                <div key={index} className="demo-key-point">
                  <point.icon className="icon" />
                  <span>{point.text}</span>
                </div>
              ))}
            </div>
          </div>
        </div> {/* End of container */}
      </section>
      {/* END OF NEW DEMO SECTION */}


      {/* Video, Features, and Benefits Section (Screenshot 45) - ADJUSTED LAYOUT */}
      <section className="video-features-benefits-section">
        <div className="container">
          <div className="row align-items-start"> {/* Main row for video and key features */}

            {/* Left Column for Video - Now larger */}
            <div className="col-lg-7 mb-4 mb-lg-0 animate-on-scroll">
              <div className="video-container">
                <video
                  ref={videoRef}
                  className="w-100 h-100 d-block" // Ensure this path is correct for your video file
                  onTimeUpdate={handleTimeUpdate}
                  onLoadedMetadata={handleLoadedMetadata}
                  onPlay={() => setIsPlaying(true)}
                  onPause={() => setIsPlaying(false)}
                  onEnded={() => setIsPlaying(false)}
                  poster="https://arizonafightsback.com/wp-content/uploads/2023/11/3-57.jpg"
                  onError={handleVideoError}
                  onCanPlay={handleCanPlay}
                  preload="metadata"
                  style={{ objectFit: 'cover' }}
                >
                   <source src={MTIVideo} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>

                {isVideoLoading && <div className="video-overlay"><div className="spinner-border text-primary" role="status"></div></div>}
                {videoError && <div className="video-overlay"><p className="text-white bg-danger p-3 rounded">{videoError}</p></div>}

                {!isPlaying && !isVideoLoading && !videoError && (
                  <div className="video-overlay" onClick={handlePlayPause}>
                    <div className="play-button"><FaPlay size={30} color="white" style={{marginLeft: '4px'}} /></div>
                  </div>
                )}

                <div className="video-controls d-flex align-items-center">
                    <button className="btn" onClick={handlePlayPause} aria-label={isPlaying ? 'Pause' : 'Play'}>{isPlaying ? <FaPause /> : <FaPlay />}</button>
                    <button className="btn" onClick={handleSkipBackward} aria-label="Skip Backward"><FaFastBackward /></button>
                    <button className="btn" onClick={handleSkipForward} aria-label="Skip Forward"><FaFastForward /></button>
                    <div className="progress-bar-container" onClick={handleProgressClick}>
                        <div className="progress-bar" style={{ width: `${(currentTime / duration) * 100}%` }}></div>
                    </div>
                    <div className="time-display">{formatTime(currentTime)} / {formatTime(duration)}</div>
                    <button className="btn" onClick={handleVolumeToggle} aria-label={isMuted ? 'Unmute' : 'Mute'}>{isMuted ? <FaVolumeOff /> : <FaVolumeUp />}</button>
                    <button className="btn" onClick={handleFullscreen} aria-label="Fullscreen"><FaExpand /></button>
                </div>
              </div>
            </div>

            {/* Right Column for Key Features List - Now smaller */}
            <div className="col-lg-5">
              <div className="feature-list-container">
                {features.map((feature, index) => (
                  <div key={index} className="feature-card">
                    <div className="feature-icon">
                      <feature.icon size={25} />
                    </div>
                    <div>
                      <h5 className="fw-bold">{feature.title}</h5>
                      <p className="mb-0">{feature.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div> {/* End of main row for video and key features */}
        </div> {/* End of container */}
      </section>
      {/* END OF VIDEO, FEATURES, AND BENEFITS SECTION */}



      {/* Section for "Why Choose Our ATS" (Benefits) - ICON REMOVED */}
      <section className="benefits-section">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center mb-4">
              {/* Changed color of this heading for better contrast with the overall page flow */}
              <h2 style={{
                fontSize: "clamp(2.2rem, 5vw, 3rem)",
                fontWeight: "800",
                paddingBottom: "1.2rem",
                marginBottom: "1rem",
                background: 'linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                textShadow: '0 0 30px rgba(0, 160, 233, 0.3)'
              }}>Why Choose Our Trading Intelligence?</h2>
            </div>
             <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {benefits.map((benefit, index) => (
              <div key={index} className="col-md-6 col-lg-3 mb-4">
                <div className="p-4 bg-white rounded-3 shadow-sm h-100 d-flex flex-column justify-content-center align-items-center text-center feature-card benefit-box">
                  {/* Removed FaCheckCircle icon here */}
                  <h6 className="fw-normal text-secondary">{benefit}</h6>
                </div>
              </div>
            ))}
          </div> {/* End of benefits row */}
        </div> {/* End of container */}
      </section>
      {/* END OF BENEFITS SECTION */}
    </div>
  );
};

export default TradingIntelligence;