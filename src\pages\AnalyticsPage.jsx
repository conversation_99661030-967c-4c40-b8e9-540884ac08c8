import React, { useEffect, useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Container, Row, Col, Button, Nav } from 'react-bootstrap';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

const AnalyticsPage = () => {
  const [activeSection, setActiveSection] = useState('customer');
  const [isIntersecting, setIsIntersecting] = useState({});
  const heroRef = useRef(null);
  const dashboardRef = useRef(null);
  const sectionRefs = {
    intro: useRef(null),
    customer: useRef(null),
    risk: useRef(null),
    finance: useRef(null),
    hr: useRef(null)
  };

  useEffect(() => {
    // Scroll to top is now handled globally by useScrollToTop hook

    // GSAP Animations
    const ctx = gsap.context(() => {
      // Hero section animations
      gsap.from(heroRef.current.querySelector('h1'), {
        y: 100,
        opacity: 0,
        duration: 1.2,
        ease: "power3.out"
      });

      gsap.from(heroRef.current.querySelector('p'), {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        delay: 0.3
      });

      gsap.from(heroRef.current.querySelectorAll('button, a'), {
        y: 30,
        opacity: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.7)",
        delay: 0.6
      });

      // Dashboard animation
      gsap.from(dashboardRef.current, {
        x: 100,
        opacity: 0,
        duration: 1.5,
        ease: "power2.out",
        delay: 0.8
      });

      // Stats animation
      gsap.from('.stat-item', {
        scale: 0,
        opacity: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.7)",
        delay: 1.2
      });

    }, heroRef);

    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.5
    };

    const observerCallback = (entries) => {
      entries.forEach(entry => {
        setIsIntersecting(prev => ({
          ...prev,
          [entry.target.id]: entry.isIntersecting
        }));

        if (entry.isIntersecting && entry.target.id !== 'intro') {
          setActiveSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    Object.values(sectionRefs).forEach(ref => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });

    return () => {
      ctx.revert();
      Object.values(sectionRefs).forEach(ref => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      });
    };
  }, []);

  const scrollToSection = (sectionId) => {
    setActiveSection(sectionId);
    sectionRefs[sectionId].current.scrollIntoView({ behavior: 'smooth' });
  };

  const analyticsTypes = [
    {
      id: 'customer',
      title: 'Customer Analytics',
      icon: 'fa-users'
    },
    {
      id: 'risk',
      title: 'Risk Analytics',
      icon: 'fa-shield-alt'
    },
    {
      id: 'finance',
      title: 'Finance Analytics',
      icon: 'fa-chart-line'
    },
    {
      id: 'hr',
      title: 'HR Analytics',
      icon: 'fa-user-tie'
    }
  ];

  return (
    <div style={{ overflowX: 'hidden' }}>
      <section
        ref={heroRef}
        className="analytics-hero-section text-white text-center d-flex align-items-center position-relative overflow-hidden"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 41, 86, 0.85), rgba(0, 41, 86, 0.95)), url("https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          minHeight: "100vh",
          padding: "8rem 0",
        }}
      >
        {/* Animated Background Elements */}
        <div className="position-absolute w-100 h-100" style={{ zIndex: 1 }}>
          {/* Floating Analytics Icons */}
          {[
            { icon: "fa-chart-pie", top: "15%", left: "10%", delay: 0 },
            { icon: "fa-chart-line", top: "25%", right: "15%", delay: 1 },
            { icon: "fa-database", bottom: "20%", left: "8%", delay: 2 },
            { icon: "fa-brain", bottom: "30%", right: "12%", delay: 3 },
          ].map((item, index) => (
            <div
              key={index}
              className="position-absolute"
              style={{
                ...item,
                width: "60px",
                height: "60px",
                background: "rgba(0, 160, 233, 0.1)",
                borderRadius: "50%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backdropFilter: "blur(10px)",
                border: "1px solid rgba(0, 160, 233, 0.2)",
                animation: `float 6s ease-in-out infinite`,
                animationDelay: `${item.delay}s`,
              }}
            >
              <i
                className={`fas ${item.icon}`}
                style={{
                  fontSize: "24px",
                  color: "#00a0e9",
                  textShadow: "0 0 10px rgba(0, 160, 233, 0.5)",
                }}
              ></i>
            </div>
          ))}

          {/* Pulse Circles */}
          {[
            { size: "200px", top: "10%", right: "20%", delay: "0s" },
            { size: "150px", bottom: "15%", left: "15%", delay: "2s" },
            { size: "100px", top: "60%", right: "10%", delay: "4s" },
          ].map((circle, index) => (
            <div
              key={index}
              className="position-absolute rounded-circle"
              style={{
                width: circle.size,
                height: circle.size,
                top: circle.top,
                bottom: circle.bottom,
                left: circle.left,
                right: circle.right,
                background: "rgba(0, 160, 233, 0.05)",
                border: "1px solid rgba(0, 160, 233, 0.1)",
                animation: `pulse 4s ease-in-out infinite`,
                animationDelay: circle.delay,
              }}
            ></div>
          ))}
        </div>

        <Container className="position-relative" style={{ zIndex: 2 }}>
          <h1
            className="display-1 fw-bolder mb-4 animate__animated animate__fadeInDown animate__slow"
            style={{
              fontSize: "3.6rem",
              fontWeight: "800",
              letterSpacing: "2.6px",
              background: "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              backgroundClip: "text",
              textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
            }}
          >
            Transform Your Data into Decisions
          </h1>
          <p
            className="lead mb-5 mx-auto animate__animated animate__fadeInUp animate__slow"
            style={{
              maxWidth: "1200px",
              textShadow: "1px 1px 3px rgba(0,0,0,0.4)",
              fontSize: "1.35rem",
            }}
          >
            We combine in-depth industry expertise with world-class technical knowledge to help you create compelling software-based products and unlock the full potential of your data.
          </p>

        </Container>
      </section>

      {/* Feature Sections */}
      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          {/* Analytics sections will be added here */}
        </Container>
      </div>

      {/* Global CSS */}
      <style>{`
        :root {
          --bs-primary: #007bff;
          --bs-primary-dark: #0056b3;
          --bs-primary-rgb: 0,123,255;
        }

        h1, h2, h3, h4, h5, h6 {
          line-height: 1.2;
        }

        p {
          line-height: 1.75;
        }

        .container {
          padding-left: 1.5rem;
          padding-right: 1.5rem;
        }

        .py-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
        .py-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
        .py-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }

        .content-wrapper {
          padding: 0;
        }

        .image-container {
          position: relative;
          overflow: hidden;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.6;
          }
          33% {
            transform: translateY(-15px) rotate(120deg);
            opacity: 1;
          }
          66% {
            transform: translateY(5px) rotate(240deg);
            opacity: 0.8;
          }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 0.15;
          }
          50% {
            transform: scale(1.1);
            opacity: 0.25;
          }
        }

        @media (min-width: 768px) {
          .py-md-5 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
          .py-md-6 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
          .py-md-7 { padding-top: 8rem !important; padding-bottom: 8rem !important; }
          .mb-md-5 { margin-bottom: 4rem !important; }
          .mb-md-6 { margin-bottom: 6rem !important; }
          .mb-md-8 { margin-bottom: 8rem !important; }
        }
      `}</style>

      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          <div className="text-center">
            <h2
              className="display-5 fw-bold"
              style={{
                fontSize: "3.6rem",
                fontWeight: "800",
                letterSpacing: "2.6px",
                background:
                  "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
              }}
            >
              Explore Our Analytics Solutions
            </h2>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            <p
              style={{
                fontSize: '1.3rem',
                color: 'rgba(255, 255, 255, 0.9)',
                maxWidth: '800px',
                margin: '0 auto',
                lineHeight: '1.7'
              }}
            >
              Discover how our analytics services can transform your business data into actionable insights
            </p>
          </div>

          <Nav
            className="justify-content-between"
            style={{
              gap: '15px',
              maxWidth: '900px',
              margin: '0 auto'
            }}
          >
            {analyticsTypes.map(type => (
              <Nav.Item key={type.id} className="flex-fill">
                <Button
                  variant="link"
                  className="w-100 text-decoration-none"
                  style={{
                    padding: '20px 15px',
                    borderRadius: '12px',
                    fontSize: '1rem',
                    fontWeight: 600,
                    color: activeSection === type.id ? 'white' : 'rgba(255, 255, 255, 0.8)',
                    background: activeSection === type.id
                      ? 'linear-gradient(135deg, #0056b3 0%, #00a0e9 100%)'
                      : 'rgba(255, 255, 255, 0.1)',
                    boxShadow: activeSection === type.id
                      ? '0 10px 20px rgba(0, 86, 179, 0.3)'
                      : '0 5px 15px rgba(0, 0, 0, 0.1)',
                    border: activeSection === type.id
                      ? 'none'
                      : '1px solid rgba(255, 255, 255, 0.2)',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '12px',
                    position: 'relative',
                    overflow: 'hidden',
                    backdropFilter: 'blur(10px)'
                  }}
                  onClick={() => scrollToSection(type.id)}
                  onMouseOver={(e) => {
                    if (activeSection !== type.id) {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
                      e.currentTarget.style.transform = 'translateY(-5px)';
                      e.currentTarget.style.color = 'white';
                    }
                  }}
                  onMouseOut={(e) => {
                    if (activeSection !== type.id) {
                      e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
                    }
                  }}
                >
                  <div
                    style={{
                      width: '60px',
                      height: '60px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: activeSection === type.id
                        ? 'rgba(255, 255, 255, 0.2)'
                        : 'rgba(0, 160, 233, 0.2)',
                      marginBottom: '5px',
                      transition: 'all 0.3s ease',
                      position: 'relative',
                      zIndex: 1
                    }}
                  >
                    <i
                      className={`fas ${type.icon}`}
                      style={{
                        fontSize: '1.8rem',
                        color: activeSection === type.id ? 'white' : '#00a0e9',
                        transition: 'all 0.3s ease'
                      }}
                    ></i>
                  </div>

                  <span style={{ position: 'relative', zIndex: 1 }}>{type.title}</span>
                </Button>
              </Nav.Item>
            ))}
          </Nav>
        </Container>
      </div>

      <div
        className="py-5 py-md-6"
        style={{
          background:
            "linear-gradient(135deg, rgba(0, 41, 86, 0.8) 0%, rgba(0, 41, 86, 0.9) 100%)",
          backdropFilter: "blur(10px)",
        }}
      >
        <Container>
          <section
            id="intro"
            ref={sectionRefs.intro}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.intro ? 1 : 0,
              transform: isIntersecting.intro ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="display-5 fw-bold text-center"
                  style={{
                    fontSize: "3.6rem",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Transforming Data into Business Insights
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row */}
            <Row className="align-items-center g-4 g-lg-5">
              <Col lg={7} md={10} className="pe-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    <strong>Makonis</strong>, as a Business Intelligence and Analytics company, has deep experience in executing BI, Analytics, Big Data and Data Science projects covering data integration, data governance, dashboards & reports, ad-hoc analysis, migration, big data analytics, data science, real-time analytics, mobile BI, cloud BI and production support.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    Our Data analytics consulting services unlocks various hidden opportunities and insights that cause a drastic effect on your business operations. We turn technology into business outcomes by delivering information management, business intelligence and analytic solutions under one roof.
                  </p>
                </div>
              </Col>
              <Col lg={5} md={10} className="ps-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                    alt="Analytics and Business Intelligence"
                    style={{
                      width: "100%",
                      height: "80%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="customer"
            ref={sectionRefs.customer}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.customer ? 1 : 0,
              transform: isIntersecting.customer ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="display-5 fw-bold text-center"
                  style={{
                    fontSize: "3.6rem",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Customer Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row */}
            <Row className="align-items-center g-4 g-lg-5">
              <Col lg={7} md={10} className="pe-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.1rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    Customer analytics, also called customer data analytics, is the systematic examination of a company's customer information and customer behavior to identify, attract and retain the most profitable customers.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.1rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    The goal of customer analytics is to create a single, accurate view of a customer to make decisions about how best to acquire and retain customers, identify high-value customers and proactively interact with them.
                  </p>

                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.1rem",
                      lineHeight: "1",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Pricing Model optimization",
                      "Churn Analysis and prevention",
                      "Data Segmentation strategies",
                      "Customer Loyalty modeling"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-3 d-flex align-items-center"
                        style={{
                          padding: "0rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "30px",
                            height: "30px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-check"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={5} md={10} className="ps-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2015&q=80"
                    alt="Customer Analytics Dashboard"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="risk"
            ref={sectionRefs.risk}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.risk ? 1 : 0,
              transform: isIntersecting.risk ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="display-5 fw-bold text-center"
                  style={{
                    fontSize: "3.6rem",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Risk Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row - Reversed */}
            <Row className="align-items-center g-4 g-lg-5 flex-row-reverse">
              <Col lg={6} md={10} className="ps-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    Risk analytics (or risk analysis) is the study of the underlying uncertainty of a given course of action. It often works in tandem with forecasting professionals to minimize future negative unforeseen effects.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    You can mitigate these risks by clearly defining, understanding, and managing tolerance for and exposure to risk. Advanced analytics capabilities enable clearer visibility into the challenges associated with managing the many types of risk.
                  </p>

                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.8",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Improve decision making with risk analysis and transparency",
                      "Reduce the cost of regulatory compliance",
                      "Dynamically evolve with adaptable risk architecture"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-3 d-flex align-items-center"
                        style={{
                          padding: "0rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-shield-alt"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={6} md={10} className="pe-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                    alt="Risk Analytics and Management"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="finance"
            ref={sectionRefs.finance}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.finance ? 1 : 0,
              transform: isIntersecting.finance ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="display-5 fw-bold text-center"
                  style={{
                    fontSize: "3.6rem",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  Finance Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row */}
            <Row className="align-items-center g-4 g-lg-5">
              <Col lg={7} md={10} className="pe-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    We all know the emerging effect of digitalization in the financial economy. Not just it is creating a requirement for the storage of huge data but also has led to a situation where the huge data needs to managed efficiently.
                  </p>
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    Typically, financial analysis is used to analyze whether an entity is stable, solvent, liquid or profitable enough to warrant a monetary investment. Financial analysts focus on the income statement, balance sheet, and cash flow statement.
                  </p>

                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.1rem",
                      lineHeight: "1.8",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Cash Flow Analysis and forecasting",
                      "ROI Analysis and optimization",
                      "P & L Analysis and reporting",
                      "Revenue Analysis and growth strategies"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-3 d-flex align-items-center"
                        style={{
                          padding: "0rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-chart-line"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={5} md={10} className="ps-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2026&q=80"
                    alt="Finance Analytics and Reporting"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>

          <section
            id="hr"
            ref={sectionRefs.hr}
            className="mb-5 mb-md-6 py-3"
            style={{
              opacity: isIntersecting.hr ? 1 : 0,
              transform: isIntersecting.hr ? 'translateY(0)' : 'translateY(30px)',
              transition: 'opacity 0.8s ease, transform 0.8s ease'
            }}
          >
            {/* Centered Heading Above Content */}
            <Row className="mb-4">
              <Col xs={12}>
                <h2
                  className="display-5 fw-bold text-center"
                  style={{
                    fontSize: "3.6rem",
                    fontWeight: "800",
                    letterSpacing: "2.6px",
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                  }}
                >
                  HR Analytics
                </h2>
              </Col>
            </Row>

            {/* Enhanced Accent Line */}
            <div className="w-30 h-1 mx-auto relative mb-5">
              <div
                className="w-full h-full rounded-sm shadow-glow"
                style={{
                  background: 'linear-gradient(90deg, transparent, #00a0e9, transparent)',
                }}
              />
            </div>

            {/* Content and Image Row - Reversed */}
            <Row className="align-items-center g-4 g-lg-5 flex-row-reverse">
              <Col lg={7} md={10} className="ps-lg-4">
                <div className="content-wrapper">
                  <p
                    className="mb-3 mb-md-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.7",
                      color: "rgba(255, 255, 255, 0.9)",
                      textAlign: "justify",
                    }}
                  >
                    Human resource analytics (HR analytics) is an area in the field of analytics that refers to applying analytic processes to the human resource department of an organization in the hope of improving employee performance and therefore getting a better return on investment.
                  </p>
                 

                  <ul
                    className="list-unstyled mt-4"
                    style={{
                      fontSize: "1.2rem",
                      lineHeight: "1.8",
                      color: "rgba(255, 255, 255, 0.9)",
                    }}
                  >
                    {[
                      "Performance Analysis and optimization",
                      "Workforce Planning and forecasting",
                      "Talent Development and training programs",
                      "Retention Analysis and improvement strategies"
                    ].map((item, index) => (
                      <li
                        key={index}
                        className="mb-3 d-flex align-items-center"
                        style={{
                          padding: "0rem 0",
                        }}
                      >
                        <div
                          className="me-3 d-flex align-items-center justify-content-center"
                          style={{
                            width: "40px",
                            height: "40px",
                            borderRadius: "50%",
                            background: "rgba(0, 160, 233, 0.2)",
                            border: "1px solid rgba(0, 160, 233, 0.3)",
                          }}
                        >
                          <i
                            className="fas fa-user-tie"
                            style={{
                              color: "#00a0e9",
                              fontSize: "16px",
                            }}
                          ></i>
                        </div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </Col>
              <Col lg={5} md={10} className="pe-lg-4">
                <div
                  className="image-container"
                  style={{
                    borderRadius: "1.25rem",
                    overflow: "hidden",
                    boxShadow: "0 0.75rem 2rem rgba(0,0,0,0.1)",
                    transition: "transform 0.4s ease-out, box-shadow 0.4s ease-out",
                    backgroundColor: "#f0f2f5",
                  }}
                >
                  <img
                    src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2026&q=80"
                    alt="Finance Analytics and Reporting"
                    style={{
                      width: "100%",
                      height: "100%",
                      minHeight: "400px",
                      objectFit: "cover",
                      transition: "transform 0.6s ease",
                    }}
                  />
                </div>
              </Col>
            </Row>
          </section>
        </Container>
      </div>

      
    </div>
  );
};

export default AnalyticsPage;